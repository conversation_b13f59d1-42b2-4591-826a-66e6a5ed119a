import aiohttp
import asyncio
import json
import logging
import os
import uuid
import re
import random
import base64
import datetime
import sys
import urllib.parse
from typing import Dict, Any
from dotenv import load_dotenv

class TeeStream:
    def __init__(self, streams):
        self.streams = streams

    def write(self, data):
        for stream in self.streams:
            stream.write(data)
            stream.flush()

    def flush(self):
        for stream in self.streams:
            stream.flush()

if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        # Terminal handler - only show INFO and above
        logging.StreamHandler(sys.stdout),
        # File handler - show all DEBUG and above
        logging.FileHandler('debug.log', encoding='utf-8', mode='w')
    ]
)

# Configure terminal handler to be less verbose
# Make sure handlers exist before trying to configure them
if logging.getLogger().handlers and len(logging.getLogger().handlers) > 0:
    logging.getLogger().handlers[0].setLevel(logging.INFO)  # Terminal shows INFO and above

    # Check if file handler exists
    if len(logging.getLogger().handlers) > 1:
        logging.getLogger().handlers[1].setLevel(logging.DEBUG)  # File shows DEBUG and above


# Create logger
logger = logging.getLogger('check_group_order')
logger.setLevel(logging.DEBUG)

# Test logging
logger.debug("Logging initialized in check_group_order.py")

# Load environment variables
load_dotenv()
PROMO_25_COOKIE = os.getenv('25_Promo')  # Using the correct case for the environment variable

DEFAULT_HEADERS = {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "cookie": PROMO_25_COOKIE if PROMO_25_COOKIE else "",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "x-csrf-token": "x",
    "origin": "https://www.ubereats.com",
    "referer": "https://www.ubereats.com/",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin"
}


# List of random names for group order members
RANDOM_NAMES = [
    "Howard", "Kongpaochicken", "IwannaEat_", "Glitchyz", "Isaiah",
    "Hammah Khan", "Mr. Method", "The method", "Parnil", "PNN21"
]

async def check_store_promo(store_url, cookie_value=None):
    """Check if store is in promo."""
    try:
        # Parse the URL to separate base URL and parameters
        parsed_url = urllib.parse.urlparse(store_url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        existing_params = dict(urllib.parse.parse_qs(parsed_url.query))

        # Ensure diningMode parameter is set
        params = {
            "diningMode": existing_params.get('diningMode', ['DELIVERY'])[0]
        }

        # Add other existing parameters
        for key, value in existing_params.items():
            if key != 'diningMode':
                params[key] = value[0]

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cookie": cookie_value or os.getenv('25_Promo')  # Use the entire cookie string
        }

        logging.info(f"Checking promo status for URL: {base_url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, params=params, headers=headers) as response:
                content = await response.text()
                cleaned_content = content.replace('\\u0022', '"')

                # Check for both promo UUIDs
                promo_uuid_1 = "2c1e668b-8d83-42a4-99db-985c72fa74de"  # US promo
                promo_uuid_2 = "807f13d5-0e00-4df2-9894-6b23e73e9fa6"  # Canada promo

                # Additional UUIDs to check
                additional_uuids = [
                    "ae2e9f79-6189-4f46-bf20-5b41debb570a",  # Old US promo UUID
                    "449baf92-dda2-44d1-9ad6-24033c26f516",
                    "c8e35ed9-3e53-4c12-a63a-7eb5a683a64f",
                    "4a6e5b6a-6784-4d83-8b90-9e9e4b3b7119",
                    "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"
                ]

                # Check for primary UUIDs in different formats
                is_promo_1 = f'"uuid":"{promo_uuid_1}"' in cleaned_content or promo_uuid_1 in cleaned_content
                is_promo_2 = f'"uuid":"{promo_uuid_2}"' in cleaned_content or promo_uuid_2 in cleaned_content

                # Check for additional UUIDs
                additional_matches = []
                for i, uuid in enumerate(additional_uuids):
                    is_match = f'"uuid":"{uuid}"' in cleaned_content or uuid in cleaned_content
                    additional_matches.append(is_match)
                    logging.info(f"Additional UUID {i+1} check: {uuid} - Found: {is_match}")

                # Also check for any UUID pattern
                uuid_pattern = r'"uuid":"([a-f0-9-]+)"'
                import re
                found_uuids = re.findall(uuid_pattern, cleaned_content)
                logging.info(f"All UUIDs found in response: {found_uuids}")

                # Save a sample of the content for debugging
                with open('common_promo_response_sample.txt', 'w') as f:
                    f.write(cleaned_content[:10000])  # Save first 10K characters
                logging.info("Saved response sample to common_promo_response_sample.txt")

                # Debug logging
                logging.info(f"Response status: {response.status}")
                logging.info(f"Promo UUID 1 check: {promo_uuid_1} - Found: {is_promo_1}")
                logging.info(f"Promo UUID 2 check: {promo_uuid_2} - Found: {is_promo_2}")

                # Save a sample of the content for debugging
                with open('common_promo_response_sample.txt', 'w') as f:
                    f.write(cleaned_content[:10000])  # Save first 10K characters
                logging.info("Saved response sample to common_promo_response_sample.txt")

                # Store is in promo if any UUID is found
                is_promo = is_promo_1 or is_promo_2 or any(additional_matches)

                if is_promo:
                    logging.info("✅ Store is in promo!")
                else:
                    logging.info("❌ Store is not in promo")

                return is_promo

    except Exception as e:
        logging.error(f"Error checking store promo: {str(e)}")
        return False

def extract_uuid_from_link(link: str) -> str:
    """Extract UUID from Uber Eats group order link."""
    pattern = r"(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)([a-zA-Z0-9-]+)(?:/join)?"
    match = re.search(pattern, link)
    if not match:
        raise ValueError("Invalid Uber Eats group order link format")
    return match.group(1)

async def make_api_request(session: aiohttp.ClientSession, endpoint: str, payload: dict) -> dict:
    try:
        url = f"https://www.ubereats.com/_p/api/{endpoint}"
        timeout = aiohttp.ClientTimeout(total=10)

        logger.debug(f"Making API request to: {url}")
        logger.debug(f"Payload: {payload}")

        async with session.post(
            url,
            json=payload,
            headers=DEFAULT_HEADERS,
            timeout=timeout
        ) as response:
            response_data = await response.json()
            logger.debug(f"API Response: {json.dumps(response_data, indent=2)}")
            return {
                "status": response.status,
                "data": response_data
            }
    except Exception as e:
        logger.error(f"API request failed: {str(e)}")
        logger.exception("Full exception:")
        return {
            "status": 500,
            "error": str(e)
        }

async def get_checkout_presentation(session: aiohttp.ClientSession, store_uuid: str, cart_uuid: str, group_uuid: str) -> dict:
    """Get checkout presentation data from the API."""
    try:
        # First set user consent
        consent_success = await set_user_consent(session)
        if not consent_success:
            logging.error("Failed to set user consent")
            return None

        checkout_payload = {
            "payloadTypes": [
                "canonicalProductStorePickerPayload",
                "cartItems",
                "subtotal",
                "basketSize",
                "promotion",
                "restrictedItems",
                "venueSectionPicker",
                "locationInfo",
                "upsellCatalogSections",
                "subTotalFareBreakdown",
                "storeSwitcherActionableBannerPayload",
                "fareBreakdown",
                "promoAndMembershipSavingBannerPayload",
                "passBanner",
                "passBannerOnCartPayload"
            ],
            "draftOrderUUID": group_uuid,
            "isGroupOrder": True,
            "cartUUID": cart_uuid,
            "storeUUID": store_uuid
        }

        result = await make_api_request(session, "getCheckoutPresentationV1", checkout_payload)

        if result["status"] != 200:
            logging.error(f"Failed to get checkout presentation: {result.get('data', {}).get('message')}")
            return None

        # Navigate through the response structure
        response_data = result.get('data', {})
        data = response_data.get('data', {})
        checkout_payloads = data.get('checkoutPayloads', {})

        # Extract both fareBreakdown and passBanner
        fare_breakdown = checkout_payloads.get('fareBreakdown', {})
        pass_banner = checkout_payloads.get('passBanner', {})

        if not fare_breakdown:
            logging.error("No fareBreakdown in checkoutPayloads")
            return None

        return {
            'fareBreakdown': fare_breakdown,
            'passBanner': pass_banner
        }

    except Exception as e:
        logging.error(f"Error getting checkout presentation: {str(e)}")
        return None

async def get_checkout_flow(session: aiohttp.ClientSession, group_order_uuid: str) -> dict:
    try:
        # Get order details using the session and group UUID
        order_details = await get_order_details_from_data(session, group_order_uuid)
        if not order_details or not order_details.get('cart_items'):
            logging.error("Failed to get order details")
            return None

        max_retries = 3
        for attempt in range(max_retries):
            draft_order_payload = {
                "isMulticart": True,
                "currencyCode": "USD",
                "deliveryType": "ASAP",
                "deliveryTime": {
                    "asap": True
                },
                "interactionType": "door_to_door",
                "useCredits": True,
                "checkMultipleDraftOrdersCap": True,
                "actionMeta": {
                    "isQuickAdd": False,
                    "numClicks": 0
                },
                "businessDetails": {},
                "extraPaymentProfiles": [],
                "promotionOptions": {
                    "autoApplyPromotionUUIDs": [],
                    "selectedPromotionInstanceUUIDs": [],
                    "skipApplyingPromotion": False
                },
                "shoppingCartItems": [{
                    "uuid": "0ba128da-ec26-5dab-bdbf-043f1112410f",
                    "shoppingCartItemUuid": "13080455-0bea-47e0-85ff-c591c9d793a0",
                    "storeUuid": "8f01b38a-d588-4511-bcfa-a4a8daa90305",
                    "sectionUuid": "833e29e1-dba0-5a06-8f85-a95fe25011ed",
                    "subsectionUuid": "16c1e0cc-d2f1-5b61-ad7e-0a750d39f170",
                    "price": 649,
                    "title": "Jumbo Jack®",
                    "quantity": 2,
                    "customizations": {},
                    "imageURL": "https://tb-static.uber.com/prod/image-proc/processed_images/2f8cbb3ac18dc27293a95f4a92ff09a4/a19bb09692310dfd41e49a96c424b3a6.jpeg",
                    "specialInstructions": "",
                    "itemId": None
                }],
                "deliveryLocation": {
                    "address": {
                        "address1": order_details['address'],
                        "city": order_details['city'],
                        "state": order_details['state'],
                        "zipcode": order_details['zipcode'],
                        "country": "US",
                        "reference": order_details['reference'],
                        "referenceType": order_details['referenceType']
                    },
                    "latitude": order_details['latitude'],
                    "longitude": order_details['longitude'],
                    "type": "delivery"
                }
            }

            draft_result = await make_api_request(session, "createDraftOrderV2", draft_order_payload)

            if draft_result.get('status') == 200 and draft_result.get('data', {}).get('status') != 'failure':
                logging.info("Draft order created successfully")
                # Extract location from draft order response
                draft_data = draft_result.get('data', {}).get('data', {})
                delivery_location = draft_data.get('deliveryLocation', {})
                address = delivery_location.get('address', {})

                # Update order_details with actual location data
                order_details.update({
                    'city': address.get('city'),
                    'state': address.get('state'),
                    'zipcode': address.get('zipcode'),
                    'address': address.get('address1')
                })

                return draft_result

            error_code = draft_result.get('data', {}).get('data', {}).get('code')
            error_message = draft_result.get('data', {}).get('data', {}).get('message')

            if error_code == '500':
                if attempt < max_retries - 1:
                    logging.warning(f"Attempt {attempt + 1} failed with 500 error, retrying...")
                    await asyncio.sleep(1)
                    continue
                else:
                    logging.error("All retry attempts failed, falling back to checkout presentation")
            elif error_code == '401' and error_message == 'NO_DELIVERY_LOCATION':
                logging.error("Missing or invalid delivery location")
                return None
            else:
                logging.error(f"Failed to create draft order - Business error: {draft_result}")
                return None

        return None

    except Exception as e:
        logging.error(f"Error in checkout flow: {str(e)}")
        return None

def extract_pass_banner_info(checkout_data: dict) -> dict:
    """Extract Uber One discount information from pass banner."""
    try:
        pass_banner = checkout_data.get('passBanner', {})
        if not pass_banner or not pass_banner.get('banners'):
            return {'uber_one_discount': 0.0}

        savings = pass_banner['banners'][0].get('savingsAmountWithCurrencySymbol', '$0.00')
        uber_one_discount = float(savings.replace('$', ''))

        return {
            'uber_one_discount': uber_one_discount,
            'savings_text': savings
        }

    except Exception as e:
        logging.error(f"Error extracting pass banner info: {str(e)}")
        return {'uber_one_discount': 0.0}

def extract_fees_from_charges(charges: list) -> dict:
    """Extract fees from charges list."""
    fees = {
        "subtotal": "0.00",
        "delivery_fee": "0.00",
        "service_fee": "0.00",
        "ca_driver_benefit": "0.00",
        "taxes": "0.00"
    }

    for charge in charges:
        title = charge.get('title', {}).get('text', '').lower()
        value_text = charge.get('value', {}).get('text', '0.00')

        if '\u00a0' in value_text:  # Split on non-breaking space
            value_text = value_text.split('\u00a0')[-1]  # Take the last value (current price)

        # Remove currency symbol and clean up
        value = value_text.replace('$', '').replace('CA', '').strip()

        logging.debug(f"Extracted fee - Title: {title}, Value: {value}")

        if "subtotal" in title:
            fees["subtotal"] = value
        elif "delivery fee" in title:
            fees["delivery_fee"] = value
        elif "taxes & other fees" in title:
            # Get the info bottom sheet paragraphs
            paragraphs = charge.get('action', {}).get('infoBottomSheet', {}).get('paragraphs', [])

            for paragraph in paragraphs:
                p_title = paragraph.get('title', '').lower()
                end_title = paragraph.get('endTitle', '$0.00').replace('$', '').strip()

                if 'service fee' in p_title:
                    fees["service_fee"] = end_title
                elif 'ca driver benefit' in p_title:
                    fees["ca_driver_benefit"] = end_title
                elif 'taxes' == p_title:
                    fees["taxes"] = end_title

    return fees

async def set_user_consent(session: aiohttp.ClientSession) -> bool:
    """Set user consent before accessing checkout."""
    try:
        payload = {
            "name": "gdpr",
            "value": True
        }

        result = await make_api_request(session, "setUserConsentV1", payload)
        return result["status"] == 200
    except Exception as e:
        logging.error(f"Error setting user consent: {str(e)}")
        return False

async def get_draft_order(session: aiohttp.ClientSession, group_uuid: str) -> dict:
    """Get draft order details."""
    try:
        payload = {
            "draftOrderUUID": group_uuid
        }

        result = await make_api_request(session, "getDraftOrderByUuidV2", payload)
        if result["status"] == 200:
            return result.get("data", {})
        return None
    except Exception as e:
        logging.error(f"Error getting draft order: {str(e)}")
        return None

def extract_fees_from_paragraphs(paragraphs: list) -> dict:
    """Extract all fees from the paragraphs list."""
    fees = {
        "service_fee": 0.0,
        "ca_driver_benefit": 0.0,
        "uber_one_discount": 0.0,
        "taxes": 0.0
    }

    for paragraph in paragraphs:
        title = paragraph.get('title', '').lower()
        end_title = paragraph.get('endTitle', '0.00')

        # Clean up the value (remove currency symbol and convert to float)
        value = float(end_title.replace('$', '').replace('CA', '').strip())

        # Extract specific fees
        if 'service fee' in title:
            fees["service_fee"] = value
        elif 'ca driver benefit' in title:
            fees["ca_driver_benefit"] = value
        elif 'uber one' in title or 'off with uber' in title:
            fees["uber_one_discount"] = abs(value)  # Make positive
        elif 'taxes' in title:
            fees["taxes"] = value

    # Calculate total fees
    total_fees = (fees["service_fee"] + fees["ca_driver_benefit"] -
                 fees["uber_one_discount"] + fees["taxes"])

    fees["total_fees"] = total_fees

    return fees

async def calculate_final_fees(session, store_id, cart_uuid, group_uuid):
    try:
        # Get checkout presentation
        checkout_data = await get_checkout_presentation(session, store_id, cart_uuid, group_uuid)
        if not checkout_data:
            return None

        # Get fare breakdown
        fare_breakdown = checkout_data.get('fareBreakdown', {})
        charges = fare_breakdown.get('charges', [])

        # Get basic fees
        fees = extract_fees_from_charges(charges)

        # Convert basic fees to float
        fee_values = {}
        for key, value in fees.items():
            try:
                cleaned_value = value.replace('CA', '').replace('$', '').strip()
                fee_values[key] = float(cleaned_value)
            except (ValueError, TypeError) as e:
                logging.error(f"Error converting fee {key} ({value}): {str(e)}")
                fee_values[key] = 0.0

        # Calculate Uber One discount (11% of subtotal)
        subtotal = fee_values.get('subtotal', 0.0)
        uber_one_discount = round(subtotal * 0.11, 2)

        # Calculate total fees before Uber One discount
        total_fees_before_discount = (fee_values.get('service_fee', 0.0) +
                                    fee_values.get('ca_driver_benefit', 0.0) +
                                    fee_values.get('taxes', 0.0))

        # Calculate final total fees after Uber One discount
        final_total_fees = total_fees_before_discount - uber_one_discount

        # Calculate total (subtotal + final fees)
        total = subtotal + final_total_fees

        result = {
            'subtotal': subtotal,
            'delivery_fee': fee_values.get('delivery_fee', 0.0),
            'service_fee': fee_values.get('service_fee', 0.0),
            'ca_driver_benefit': fee_values.get('ca_driver_benefit', 0.0),
            'uber_one_discount': uber_one_discount,
            'taxes': fee_values.get('taxes', 0.0),
            'total_fees': final_total_fees,
            'final_fees': final_total_fees,  # Add this key for compatibility
            'total': total,  # Add total calculation
            'total_fees_before_discount': total_fees_before_discount,
            'has_free_delivery': fee_values.get('delivery_fee', 0.0) < 0.01
        }

        logging.info(f"Calculated fees: {json.dumps(result, indent=2)}")
        return result

    except Exception as e:
        logging.error(f"Error calculating final fees: {str(e)}")
        logging.debug("Full error:", exc_info=True)
        return {
            'subtotal': 0.0,
            'delivery_fee': 0.0,
            'service_fee': 0.0,
            'ca_driver_benefit': 0.0,
            'uber_one_discount': 0.0,
            'taxes': 0.0,
            'total_fees': 0.0,
            'final_fees': 0.0,  # Add this key for compatibility
            'total': 0.0,  # Add total calculation
            'total_fees_before_discount': 0.0,
            'has_free_delivery': False
        }

async def validate_session(session):
    test_result = await make_api_request(session, "getActiveOrdersV1", {})
    return test_result['status'] == 200

def extract_delivery_details_from_url(url: str) -> dict:
    """Extract delivery details from the pl parameter in URL."""
    try:
        # Extract and decode the pl parameter
        pl_match = re.search(r'pl=([^&]+)', url)
        if pl_match:
            pl_data = base64.b64decode(pl_match.group(1)).decode('utf-8')
            delivery_json = json.loads(pl_data)

            return {
                "location": {
                    "address": {
                        "address1": delivery_json.get("address", ""),
                        "reference": delivery_json.get("reference", ""),
                        "referenceType": delivery_json.get("referenceType", "uber_places"),
                        "country": "US"
                    },
                    "latitude": delivery_json.get("latitude"),
                    "longitude": delivery_json.get("longitude")
                }
            }
    except Exception as e:
        logging.error(f"Error extracting delivery details: {str(e)}")
        return {}



async def extract_store_id(url: str) -> tuple[str, str]:
    """Extract store ID from UberEats URL."""
    try:
        # Match store ID pattern from URL
        # Example: /store/mcdonalds-junipero-serra/2ty6Z2bzTm-qM_B7PL8sKg
        store_id_match = re.search(r'/store/[^/]+/([^?]+)', url)
        if store_id_match:
            return store_id_match.group(1), "store"

        # If no match found
        return None, None

    except Exception as e:
        logging.error(f"Error extracting store ID: {str(e)}")
        return None, None

def parse_store_url(url: str) -> dict:
    """Parse and log all components of a store URL."""
    try:
        parsed = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed.query)

        # Extract store ID using regex
        store_id_match = re.search(r"/store/[^/]+/([a-zA-Z0-9-_]+)", parsed.path)
        if not store_id_match:
            logging.error("Could not extract store ID from URL")
            return None

        store_id = store_id_match.group(1)

        # Decode the location payload
        encoded_pl = query_params.get('pl', [None])[0]
        location_data = None
        if encoded_pl:
            try:
                decoded_pl = base64.b64decode(encoded_pl).decode('utf-8')
                location_data = json.loads(decoded_pl)
            except Exception as e:
                logging.error(f"Error decoding location payload: {str(e)}")

        # Create return dictionary with safe defaults
        return_data = {
            'store_id': store_id,
            'location_data': location_data or {},
            'query_params': dict(query_params)
        }

        # Log parsed data
        logging.info("=== Store URL Analysis ===")
        logging.info(f"Store ID: {store_id}")
        logging.info(f"Query Parameters: {json.dumps(dict(query_params), indent=2)}")
        logging.info(f"Location Data: {json.dumps(location_data, indent=2)}")

        return return_data

    except Exception as e:
        logging.error(f"Error parsing store URL: {str(e)}")
        return None

async def check_group_order(url: str) -> bool:
    """Check if the group order is valid and accessible."""
    # Extract UUID from the URL
    uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', url)
    if not uuid_match:
        logging.error("Failed to extract UUID from group order URL")
        return False

    group_uuid = uuid_match.group(1)

    # Create a session with the cookie
    cookie = os.getenv('25_Promo')
    session = aiohttp.ClientSession()

    # Parse the cookie string into individual cookies
    if cookie:
        cookie_parts = cookie.split(';')
        for part in cookie_parts:
            if '=' in part:
                name, value = part.strip().split('=', 1)
                session.cookie_jar.update_cookies({name: value})

    try:
        if not await validate_session(session):
            logging.error("Invalid session")
            await session.close()
            return False

        # Get cart details to verify the group order is valid
        order_details = await get_order_details_from_data(session, group_uuid)
        if not order_details:
            logging.error("Failed to get order details")
            await session.close()
            return False

        await session.close()
        return True

    except Exception as e:
        logging.error(f"Error checking group order: {str(e)}")
        await session.close()
        return False

async def get_order_details_from_data(session_or_data, data=None):
    """
    Extract order details from API response data.

    This function can be called in two ways:
    1. get_order_details_from_data(session, group_uuid) - to fetch data using the session
    2. get_order_details_from_data(data) - to process already fetched data
    """
    try:
        # Determine if this is a session+uuid call or just data processing
        if isinstance(session_or_data, aiohttp.ClientSession):
            # This is a session, so we need to fetch the data
            session = session_or_data
            group_uuid = data

            # Fetch draft order data
            payload = {"draftOrderUUID": group_uuid}
            result = await make_api_request(session, "getDraftOrderByUuidV2", payload)

            if result["status"] != 200:
                logging.error("Failed to get draft order data")
                return None

            # Extract the data from the response
            data = result.get("data", {}).get("data", {})
        else:
            # This is already the data
            data = session_or_data

        if not isinstance(data, dict):
            logging.error("Invalid data format")
            return None

        # Get store UUID
        store_uuid = data.get('storeUuid')
        if not store_uuid:
            logging.error("No store UUID found in response")
            return None

        delivery_address = data.get('deliveryAddress', {})
        if not delivery_address:
            logging.error("No delivery address found in response")
            return None

        address_details = delivery_address.get('address', {})

        # Extract city and state from subtitle
        subtitle = address_details.get('subtitle', '')
        city = state = ""
        if subtitle:
            parts = subtitle.split(', ')
            if len(parts) >= 2:
                city = parts[-2]
                state = parts[-1]

        # Extract store information from the API response
        store_info = {}

        # Try to get store name from various locations in the response
        store_name = None
        if 'store' in data and data['store']:
            store_data = data['store']
            store_name = store_data.get('title') or store_data.get('name')

            # Extract store image/banner if available
            if 'heroImageUrl' in store_data:
                store_info['banner_image'] = store_data['heroImageUrl']
            elif 'imageUrl' in store_data:
                store_info['banner_image'] = store_data['imageUrl']

            # Extract additional store metadata
            if 'rating' in store_data:
                store_info['rating'] = store_data['rating']
            if 'cuisineType' in store_data:
                store_info['cuisine_type'] = store_data['cuisineType']
            if 'deliveryFee' in store_data:
                store_info['delivery_fee'] = store_data['deliveryFee']
            if 'estimatedDeliveryTime' in store_data:
                store_info['estimated_delivery_time'] = store_data['estimatedDeliveryTime']

            # Extract delivery time from various possible locations
            if 'deliveryTime' in store_data:
                store_info['delivery_time'] = store_data['deliveryTime']
            elif 'eta' in store_data:
                store_info['delivery_time'] = store_data['eta']
            elif 'estimatedDeliveryTimeRange' in store_data:
                time_range = store_data['estimatedDeliveryTimeRange']
                if isinstance(time_range, dict):
                    min_time = time_range.get('min', '')
                    max_time = time_range.get('max', '')
                    if min_time and max_time:
                        store_info['delivery_time'] = f"{min_time}-{max_time} min"

            # Extract store hours/operating status
            if 'hours' in store_data:
                store_info['hours'] = store_data['hours']
            elif 'operatingHours' in store_data:
                store_info['hours'] = store_data['operatingHours']
            elif 'storeHours' in store_data:
                store_info['hours'] = store_data['storeHours']

            # Extract current operating status
            if 'isOpen' in store_data:
                store_info['is_open'] = store_data['isOpen']
            elif 'status' in store_data:
                store_info['status'] = store_data['status']
            elif 'operatingStatus' in store_data:
                store_info['operating_status'] = store_data['operatingStatus']

        # Fallback: try to get store name from storeTitle field
        if not store_name and 'storeTitle' in data:
            store_name = data['storeTitle']

        # Add store name to store_info
        if store_name:
            store_info['name'] = store_name

        # Try to extract delivery time from main data object if not found in store data
        if 'delivery_time' not in store_info:
            if 'estimatedDeliveryTime' in data:
                store_info['delivery_time'] = data['estimatedDeliveryTime']
            elif 'deliveryEstimate' in data:
                estimate = data['deliveryEstimate']
                if isinstance(estimate, dict):
                    if 'displayString' in estimate:
                        store_info['delivery_time'] = estimate['displayString']
                    elif 'minEstimate' in estimate and 'maxEstimate' in estimate:
                        min_est = estimate['minEstimate']
                        max_est = estimate['maxEstimate']
                        store_info['delivery_time'] = f"{min_est}-{max_est} min"

        return {
            'store_id': store_uuid,
            'store_info': store_info,
            'cart_items': data.get('shoppingCart', {}).get('items', []),
            'address': address_details.get('address1', ''),
            'city': city,
            'state': state,
            'zipcode': address_details.get('zipcode'),
            'latitude': delivery_address.get('latitude'),
            'longitude': delivery_address.get('longitude'),
            'reference': delivery_address.get('reference', ''),
            'referenceType': delivery_address.get('referenceType', 'uber_places')
        }

    except Exception as e:
        logging.error(f"Error getting order details: {str(e)}")
        return None

async def process_item(item: Dict[str, Any]) -> Dict[str, Any]:
    """Process an item from the cart and extract its details."""
    logging.info(f"Processing item data:\n{json.dumps(item, indent=2)}")

    result = {
        'title': item.get('title', 'Unknown Item'),
        'price': float(item.get('price', 0)),
        'quantity': item.get('quantity', 1),
        'promos': []
    }

    # Check for promoInfo
    if 'promoInfo' in item:
        promo_info = item['promoInfo']
        if isinstance(promo_info, dict) and 'promoInfoLabel' in promo_info:
            label_info = promo_info['promoInfoLabel']

            # First try accessibilityText
            promo_label = label_info.get('accessibilityText')

            # If not found, try to get from richTextElements
            if not promo_label and 'richTextElements' in label_info:
                for element in label_info['richTextElements']:
                    if (element.get('type') == 'text' and
                        'text' in element and
                        isinstance(element['text'], dict) and
                        'text' in element['text'] and
                        isinstance(element['text']['text'], dict) and
                        'text' in element['text']['text']):
                        promo_label = element['text']['text']['text']
                        break

            # Add the promo label if found
            if promo_label:
                result['promos'].append(promo_label)

    # Add any customizations if present
    if 'customizations' in item and item['customizations']:
        result['customizations'] = item['customizations']

    # Add special instructions if present
    if 'specialInstructions' in item and item['specialInstructions']:
        result['special_instructions'] = item['specialInstructions']

    return result

async def get_store_url_from_data(session: aiohttp.ClientSession, data: dict) -> str:
    try:
        # Get store UUID and delivery details
        store_uuid = data.get('storeUuid')
        delivery_address = data.get('deliveryAddress', {})

        if not store_uuid or not delivery_address:
            # Try alternate data structure
            store_uuid = data.get('store', {}).get('storeUuid')
            delivery_address = data.get('address', {})

            if not store_uuid:
                logging.error("Could not find store UUID in data")
                return None

        # Construct location payload
        location_payload = {
            "address": delivery_address.get('address1', ''),
            "reference": delivery_address.get('reference', ''),
            "referenceType": delivery_address.get('referenceType', 'uber_places'),
            "latitude": delivery_address.get('latitude'),
            "longitude": delivery_address.get('longitude')
        }

        # Get store details from API
        store_result = await make_api_request(session, "getStoreV1", {
            "storeUuid": store_uuid,
            "location": location_payload
        })

        if store_result.get('status') != 200:
            logging.error("Failed to get store details")
            return None

        # Extract store data from nested structure
        store_data = store_result.get('data', {}).get('data', {})
        store_slug = store_data.get('slug')

        if not store_slug:
            logging.error(f"Failed to get store slug. Store data: {store_data}")
            return None

        # Properly encode the location payload
        json_str = json.dumps(location_payload)
        base64_encoded = base64.b64encode(json_str.encode()).decode()
        encoded_pl = urllib.parse.quote(base64_encoded)

        # Construct the final URL
        return (
            f"https://www.ubereats.com/store/{store_slug}/{store_uuid}"
            f"?diningMode=DELIVERY&pl={encoded_pl}&ps=1"
        )

    except Exception as e:
        logging.error(f"Error getting store URL: {str(e)}")
        return None

async def process_group_order(group_link: str) -> dict:
    try:
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', group_link)
        if not uuid_match:
            return None

        group_uuid = uuid_match.group(1)
        current_cookie = os.getenv('25_Promo')
        session = None

        try:
            # Create a session with the cookie
            session = aiohttp.ClientSession()

            # Parse the cookie string into individual cookies
            if current_cookie:
                cookie_parts = current_cookie.split(';')
                for part in cookie_parts:
                    if '=' in part:
                        name, value = part.strip().split('=', 1)
                        session.cookie_jar.update_cookies({name: value})

            logging.info(f"Created session with cookie: {current_cookie[:50] if current_cookie else 'None'}...")
            # Initial join and data fetch with regular cookie
            join_payload = {
                "draftOrderUuid": group_uuid,
                "nickname": random.choice(RANDOM_NAMES)
            }

            join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload)

            if (join_result.get('data', {}).get('data', {}).get('message') == 'cart.not_editable.group_order_locked' or
                join_result.get('data', {}).get('message') == 'cart.not_editable.group_order_locked'):
                return {
                    'error': {
                        'type': 'LOCKED_ORDER',
                        'message': 'cart.not_editable.group_order_locked'
                    }
                }

            data = join_result.get('data', {}).get('data', {})
            if not data:
                logging.error("No data received from join request")
                return None

            store_url = await get_store_url_from_data(session, data)
            if store_url:
                logging.info(f"[LINK] Generated store URL: {store_url}")
            else:
                logging.error("Failed to generate store URL")

            # Only proceed with these checks if we have valid data
            if not await validate_session(session):
                logging.error("Invalid session")
                return None

            # Process the data we already have
            order_details = await get_order_details_from_data(data)
            if not order_details:
                logging.error("Failed to get order details")
                return None

            logging.info(f"Cart details: {json.dumps(order_details, indent=2)}")

            # Get checkout presentation and calculate fees
            checkout_data = await get_checkout_presentation(
                session,
                order_details['store_id'],
                data.get('cartUuid'),
                group_uuid
            )

            fees_data = None
            if checkout_data:
                fees_data = await calculate_final_fees(
                    session,
                    order_details['store_id'],
                    data.get('cartUuid'),
                    group_uuid
                )

            return {
                'cart_items': order_details.get('cart_items', []),
                'location': {
                    'address': order_details.get('address'),
                    'city': order_details.get('city'),
                    'state': order_details.get('state'),
                    'zipcode': order_details.get('zipcode')
                },
                'store_info': order_details.get('store_info', {}),
                'store_url': store_url,
                'fees': fees_data
            }

        finally:
            if session:
                await session.close()

    except Exception as e:
        logging.error(f"Error processing group order: {str(e)}")
        return None

async def main():
    try:
        if not os.getenv('25_Promo'):
            logging.error("25_Promo cookie not found in .env file")
            return

        while True:
            try:
                print("\nEnter group order link (or 'exit' to quit):")
                group_link = input().strip()
                logging.info(f"Input received: {group_link}")

                if group_link.lower() == 'exit':
                    logging.info("Exiting program")
                    break

                if not group_link:
                    logging.error("Invalid group order link")
                    continue

                result = await process_group_order(group_link)
                if result:
                    logging.info("✅ Order processed successfully!")

                    # Get location from the result
                    location = result.get('location', {})
                    address_str = location.get('address', 'Unknown Address')
                    city_str = location.get('city', '')
                    state_str = location.get('state', '')

                    # Print location information
                    print(f"Delivery Address: {address_str}")
                    if city_str and state_str:
                        print(f"City/State: {city_str}, {state_str}")

                    # Process cart items
                    cart_items = []
                    total_before_discount = 0
                    if 'cart_items' in result:
                        print("\nItems in cart:")
                        for item in result['cart_items']:
                            price = item.get('price', 0)
                            quantity = item.get('quantity', 1)
                            title = item.get('title', 'Unknown Item')
                            item_str = f"{title} x{quantity} (${price/100:.2f})"
                            cart_items.append(item_str)
                            total_before_discount += (price * quantity) / 100
                            print(f"- {item_str}")

                    # Store URL if available
                    if result.get('store_url'):
                        print(f"\nStore URL: {result.get('store_url')}")

                    # Process fees information
                    if result.get('fees'):
                        fees = result['fees']
                        print("\nPrice breakdown:")
                        print(f"Subtotal: ${fees.get('subtotal', 0):.2f}")

                        # Print fee details
                        if 'service_fee' in fees:
                            print(f"Service Fee: ${fees['service_fee']:.2f}")
                        if 'delivery_fee' in fees:
                            print(f"Delivery Fee: ${fees['delivery_fee']:.2f}")
                        if 'taxes' in fees:
                            print(f"Taxes: ${fees['taxes']:.2f}")
                        if 'uber_one_discount' in fees and fees['uber_one_discount'] > 0:
                            print(f"Uber One Discount: -${fees['uber_one_discount']:.2f}")

                        # Print final totals
                        if 'final_fees' in fees:
                            print(f"Final Fees: ${fees['final_fees']:.2f}")
                        if 'total' in fees:
                            print(f"Total: ${fees['total']:.2f}")
                    else:
                        print("\nCould not retrieve fee information.")

            except EOFError:
                logging.info("\nExiting program...")
                break
            except KeyboardInterrupt:
                logging.info("\nExiting program...")
                break
            except Exception as e:
                logging.error(f"Error processing group order: {str(e)}")
                logging.debug("Full error:", exc_info=True)
                continue
    finally:
        # Add session end marker
        logging.info("\n" + "="*50)
        logging.info(f"Session Ended: {datetime.datetime.now()}")
        logging.info("="*50 + "\n")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting program...")
    except Exception as e:
        logging.error(f"Fatal error: {str(e)}")