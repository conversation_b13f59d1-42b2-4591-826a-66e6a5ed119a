2025-04-05 13:38:54,425 - main - INFO - Running all bots simultaneously...
2025-04-05 13:38:54,445 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:38:54,495 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:38:54,501 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:38:56,034 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,041 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,048 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,143 - run_all_bots - INFO - All bots have been shut down.
2025-04-05 13:42:44,253 - main - INFO - Running all bots simultaneously...
2025-04-05 13:42:44,269 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:42:44,277 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:42:44,282 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:42:45,618 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,620 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,629 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,723 - run_all_bots - INFO - All bots have been shut down.
2025-04-05 13:45:23,684 - main - INFO - Running all bots simultaneously...
2025-04-05 13:45:23,691 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:45:23,698 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:45:23,703 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:45:25,111 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:45:25,153 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:45:25,182 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 09:57:56,822 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 11:36:20,950 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 11:40:48,319 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:25,634 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 02:56:25,639 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 02:56:40,153 - main - INFO - Running all bots simultaneously...
2025-04-10 02:56:40,162 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 02:56:40,169 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 02:56:40,174 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 02:56:41,603 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:41,628 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:41,649 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:15,712 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 03:04:15,712 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 03:04:22,159 - main - INFO - Running all bots simultaneously...
2025-04-10 03:04:22,166 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:04:22,175 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:04:22,179 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:04:23,475 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:23,501 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:23,527 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:39,607 - main - INFO - Running all bots simultaneously...
2025-04-10 03:09:39,615 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:09:39,622 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:09:39,626 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:09:41,103 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:41,112 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:41,132 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:42,644 - main - INFO - Running all bots simultaneously...
2025-04-10 03:13:42,652 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:13:42,660 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:13:42,666 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:13:43,981 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:43,982 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:43,990 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:32,961 - main - INFO - Running all bots simultaneously...
2025-04-10 03:18:32,970 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:18:32,979 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:18:32,988 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:18:34,689 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:34,690 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:34,704 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:48,177 - main - INFO - Running all bots simultaneously...
2025-04-10 03:19:48,185 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:19:48,195 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:19:48,201 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:19:49,506 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:49,540 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:49,551 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:28:02,978 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 08:32:49,600 - main - INFO - Running all bots simultaneously...
2025-04-10 08:32:49,609 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 08:32:49,617 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 08:32:49,624 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 08:32:51,662 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:32:51,674 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:32:51,677 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 10:39:56,229 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 10:39:56,229 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 12:04:32,351 - main - INFO - Running all bots simultaneously...
2025-04-10 12:04:32,360 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 12:04:32,368 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 12:04:32,374 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 12:04:33,759 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:04:33,826 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:04:33,830 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:14:53,277 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 12:14:53,277 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-10 12:14:53,280 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 13:17:59,053 - main - INFO - Running all bots simultaneously...
2025-04-10 13:17:59,059 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 13:17:59,065 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 13:17:59,069 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 13:17:59,710 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 13:17:59,711 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 13:17:59,713 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:23:23,817 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 18:23:23,823 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 18:23:31,065 - main - INFO - Running all bots simultaneously...
2025-04-10 18:23:31,072 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 18:23:31,081 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 18:23:31,085 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 18:23:31,800 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:23:31,800 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:23:31,803 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:44:16,523 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 18:44:16,524 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 18:44:21,165 - main - INFO - Running all bots simultaneously...
2025-04-10 18:44:21,171 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 18:44:21,179 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 18:44:21,183 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 18:44:21,905 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:44:21,909 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 18:44:21,921 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:03:55,489 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 19:03:55,489 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 19:04:01,043 - main - INFO - Running all bots simultaneously...
2025-04-10 19:04:01,050 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 19:04:01,061 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 19:04:01,066 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 19:04:01,979 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:04:02,000 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:06:42,763 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 19:06:42,763 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 19:06:47,842 - main - INFO - Running all bots simultaneously...
2025-04-10 19:06:47,850 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 19:06:47,860 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 19:06:47,864 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 19:06:48,761 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:06:48,776 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:06:48,780 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:54:41,561 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 19:54:41,564 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 19:54:47,376 - main - INFO - Running all bots simultaneously...
2025-04-10 19:54:47,386 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 19:54:47,395 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 19:54:47,400 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 19:54:48,245 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:54:48,245 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 19:54:48,245 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 20:43:55,798 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 20:43:55,801 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 20:44:01,357 - main - INFO - Running all bots simultaneously...
2025-04-10 20:44:01,365 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 20:44:01,372 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 20:44:01,377 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 20:44:02,181 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 20:44:02,188 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 20:44:02,188 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 00:37:28,594 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 00:37:28,602 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 00:37:48,313 - main - INFO - Running all bots simultaneously...
2025-04-11 00:37:48,324 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 00:37:48,334 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 00:37:48,339 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 00:37:49,030 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 00:37:49,030 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:01:49,974 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 02:01:49,978 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 02:02:07,391 - main - INFO - Running all bots simultaneously...
2025-04-11 02:02:07,398 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 02:02:07,406 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 02:02:07,410 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 02:02:08,162 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:02:08,163 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:02:08,165 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:13:33,338 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 02:13:33,338 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 02:13:38,705 - main - INFO - Running all bots simultaneously...
2025-04-11 02:13:38,713 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 02:13:38,720 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 02:13:38,725 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 02:13:39,882 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:13:39,883 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:13:39,897 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:22:48,560 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 02:22:48,560 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 02:22:53,380 - main - INFO - Running all bots simultaneously...
2025-04-11 02:22:53,388 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 02:22:53,393 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 02:22:53,397 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 02:22:54,003 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:22:54,006 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 02:22:54,009 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:06:37,456 - main - INFO - Bots stopped by keyboard interrupt
2025-04-11 13:06:41,556 - main - INFO - Running all bots simultaneously...
2025-04-11 13:06:41,562 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:06:41,570 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:06:41,573 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:06:42,344 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:06:42,345 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:06:42,345 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:15:17,779 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:15:17,780 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:15:21,360 - main - INFO - Running all bots simultaneously...
2025-04-11 13:15:21,368 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:15:21,378 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:15:21,383 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:15:22,260 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:15:22,260 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:25:21,209 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:25:21,209 - run_all_bots - INFO - Terminating budgetbot...
2025-04-11 13:25:21,221 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:25:25,265 - main - INFO - Running all bots simultaneously...
2025-04-11 13:25:25,272 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:25:25,280 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:25:25,283 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:25:26,099 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:25:26,099 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:33:29,454 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:33:29,455 - run_all_bots - INFO - Terminating budgetbot...
2025-04-11 13:33:29,461 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:33:33,729 - main - INFO - Running all bots simultaneously...
2025-04-11 13:33:33,734 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:33:33,742 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:33:33,746 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:33:34,338 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:33:34,341 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:33:34,345 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:39:36,788 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:39:36,789 - run_all_bots - INFO - Terminating budgetbot...
2025-04-11 13:39:36,789 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:39:40,363 - main - INFO - Running all bots simultaneously...
2025-04-11 13:39:40,371 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:39:40,378 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:39:40,382 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:39:40,985 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:39:40,993 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:39:40,999 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:43:28,614 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:43:28,615 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-11 13:43:28,615 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:43:32,374 - main - INFO - Running all bots simultaneously...
2025-04-11 13:43:32,381 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:43:32,387 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:43:32,391 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:43:33,075 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:43:33,079 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:43:33,080 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:50:10,157 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:50:10,157 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:50:13,019 - main - INFO - Running all bots simultaneously...
2025-04-11 13:50:13,025 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:50:13,033 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:50:13,039 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:50:13,695 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:50:13,695 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:50:13,697 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:53:36,250 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:53:36,250 - run_all_bots - INFO - Terminating budgetbot...
2025-04-11 13:53:36,253 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:53:41,305 - main - INFO - Running all bots simultaneously...
2025-04-11 13:53:41,312 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:53:41,318 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:53:41,323 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:53:41,965 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:53:41,981 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:53:41,983 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:57:53,270 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 13:57:53,270 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 13:57:56,258 - main - INFO - Running all bots simultaneously...
2025-04-11 13:57:56,266 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 13:57:56,275 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 13:57:56,279 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 13:57:57,028 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:57:57,030 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 13:57:57,031 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:01:36,255 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:01:36,256 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:01:39,333 - main - INFO - Running all bots simultaneously...
2025-04-11 14:01:39,339 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:01:39,347 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:01:39,351 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:01:40,020 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:01:40,022 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:01:40,032 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:12:49,360 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:12:49,361 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:12:53,731 - main - INFO - Running all bots simultaneously...
2025-04-11 14:12:53,740 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:12:53,751 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:12:53,756 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:12:54,641 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:12:54,642 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:12:54,643 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:32:15,088 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:32:15,090 - run_all_bots - INFO - Terminating budgetbot...
2025-04-11 14:32:15,091 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-11 14:32:15,095 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:32:18,188 - main - INFO - Running all bots simultaneously...
2025-04-11 14:32:18,195 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:32:18,204 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:32:18,207 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:32:18,971 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:32:18,995 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:32:18,999 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:33:07,588 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:33:07,589 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-11 14:33:07,592 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:33:10,016 - main - INFO - Running all bots simultaneously...
2025-04-11 14:33:10,023 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:33:10,030 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:33:10,034 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:33:10,671 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:33:10,680 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:33:10,680 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:38:51,059 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:38:51,060 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:38:53,324 - main - INFO - Running all bots simultaneously...
2025-04-11 14:38:53,331 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:38:53,337 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:38:53,342 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:38:53,870 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:38:53,952 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:38:53,952 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:38:53,989 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:44:13,587 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:44:13,588 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:44:16,124 - main - INFO - Running all bots simultaneously...
2025-04-11 14:44:16,135 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:44:16,146 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:44:16,152 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:44:16,943 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:44:17,045 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:44:17,050 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:50:06,114 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:50:06,114 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:50:08,635 - main - INFO - Running all bots simultaneously...
2025-04-11 14:50:08,642 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:50:08,647 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:50:08,652 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:50:09,256 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:50:09,345 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:50:09,352 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:50:09,352 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:53:26,808 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:53:26,809 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:53:29,016 - main - INFO - Running all bots simultaneously...
2025-04-11 14:53:29,024 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:53:29,034 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:53:29,040 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:53:29,707 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:53:29,841 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:53:29,846 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:53:29,852 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:57:12,028 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:57:12,028 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:57:14,352 - main - INFO - Running all bots simultaneously...
2025-04-11 14:57:14,362 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:57:14,374 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:57:14,380 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:57:15,034 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:57:15,184 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:57:15,212 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:57:15,213 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:58:56,911 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 14:58:56,911 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 14:58:59,237 - main - INFO - Running all bots simultaneously...
2025-04-11 14:58:59,248 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 14:58:59,260 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 14:58:59,266 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 14:59:00,061 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 14:59:00,189 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:59:00,222 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 14:59:00,239 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:08:35,039 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:08:35,039 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:08:37,825 - main - INFO - Running all bots simultaneously...
2025-04-11 15:08:37,834 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:08:37,844 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:08:37,849 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:08:38,432 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:08:38,527 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:08:38,527 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:08:38,527 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:10:52,702 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:10:52,702 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-11 15:10:52,703 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:10:55,111 - main - INFO - Running all bots simultaneously...
2025-04-11 15:10:55,118 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:10:55,126 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:10:55,131 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:10:55,688 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:10:55,746 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:10:55,750 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:10:55,783 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:15:32,284 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:15:32,285 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:15:34,522 - main - INFO - Running all bots simultaneously...
2025-04-11 15:15:34,530 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:15:34,538 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:15:34,542 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:15:35,056 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:15:35,152 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:15:35,178 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:15:35,181 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:18:53,310 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:18:53,311 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:18:55,654 - main - INFO - Running all bots simultaneously...
2025-04-11 15:18:55,663 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:18:55,671 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:18:55,675 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:18:56,536 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:18:56,642 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:18:56,686 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:18:56,689 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:23:23,103 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:23:23,104 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:23:26,237 - main - INFO - Running all bots simultaneously...
2025-04-11 15:23:26,244 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:23:26,252 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:23:26,256 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:23:27,085 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:23:27,086 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 15:23:27,086 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 15:23:27,129 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:23:27,154 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:23:27,190 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:32:50,242 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:32:50,242 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:32:53,363 - main - INFO - Running all bots simultaneously...
2025-04-11 15:32:53,370 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:32:53,378 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:32:53,382 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:32:53,918 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:32:53,919 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 15:32:53,919 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 15:32:53,995 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:32:54,005 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:32:54,005 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:35:35,997 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:35:35,998 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:35:46,335 - main - INFO - Running all bots simultaneously...
2025-04-11 15:35:46,341 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:35:46,348 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:35:46,352 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:35:46,861 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:35:46,861 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 15:35:46,861 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 15:35:46,955 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:35:46,956 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:35:46,959 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:37:31,898 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 15:37:31,898 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 15:37:33,871 - main - INFO - Running all bots simultaneously...
2025-04-11 15:37:33,878 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 15:37:33,885 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 15:37:33,890 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 15:37:34,553 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 15:37:34,553 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 15:37:34,553 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 15:37:34,673 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:37:34,720 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 15:37:34,725 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 16:10:15,708 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 16:10:15,708 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 16:10:17,643 - main - INFO - Running all bots simultaneously...
2025-04-11 16:10:17,651 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 16:10:17,659 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 16:10:17,663 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 16:10:18,343 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 16:10:18,343 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 16:10:18,343 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 16:10:18,497 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 16:10:18,497 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 16:10:18,498 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 17:32:14,833 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 17:32:14,835 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 17:32:18,538 - main - INFO - Running all bots simultaneously...
2025-04-11 17:32:18,547 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 17:32:18,555 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 17:32:18,559 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 17:32:19,307 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 17:32:19,308 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 17:32:19,308 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 17:32:19,430 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 17:32:19,430 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 17:32:19,430 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 18:32:08,037 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 18:32:08,042 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 18:32:10,756 - main - INFO - Running all bots simultaneously...
2025-04-11 18:32:10,763 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 18:32:10,773 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 18:32:10,777 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 18:32:11,650 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 18:32:11,650 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 18:32:11,650 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 18:32:11,806 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 18:32:11,806 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 21:01:33,084 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-11 21:01:33,090 - run_all_bots - INFO - All bots have been shut down.
2025-04-11 21:01:35,477 - main - INFO - Running all bots simultaneously...
2025-04-11 21:01:35,488 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-11 21:01:35,499 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-11 21:01:35,504 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-11 21:01:36,326 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-11 21:01:36,326 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-11 21:01:36,326 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-11 21:01:36,581 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 21:01:36,581 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-11 21:01:36,581 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:11:29,712 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 00:11:29,716 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 00:11:29,728 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 00:11:32,646 - main - INFO - Running all bots simultaneously...
2025-04-12 00:11:32,654 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 00:11:32,661 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 00:11:32,666 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 00:11:33,353 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 00:11:33,353 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 00:11:33,354 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 00:11:33,534 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:11:33,534 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:11:33,535 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:24:13,990 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 00:24:13,990 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 00:24:16,684 - main - INFO - Running all bots simultaneously...
2025-04-12 00:24:16,691 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 00:24:16,698 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 00:24:16,702 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 00:24:17,212 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 00:24:17,212 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 00:24:17,212 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 00:24:17,311 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:24:17,353 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 00:24:17,360 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:07:42,265 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:07:42,266 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 01:07:42,268 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:07:44,288 - main - INFO - Running all bots simultaneously...
2025-04-12 01:07:44,294 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:07:44,300 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:07:44,305 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:07:44,876 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:07:44,877 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:07:44,877 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:07:44,969 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:07:44,970 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:07:44,977 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:11:21,240 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:11:21,242 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:11:23,405 - main - INFO - Running all bots simultaneously...
2025-04-12 01:11:23,411 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:11:23,418 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:11:23,421 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:11:23,950 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:11:23,951 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:11:23,951 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:11:24,028 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:11:24,070 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:11:24,070 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:16:58,027 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:16:58,028 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:17:00,099 - main - INFO - Running all bots simultaneously...
2025-04-12 01:17:00,105 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:17:00,112 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:17:00,116 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:17:00,717 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:17:00,717 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:17:00,717 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:17:00,818 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:17:00,818 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:17:00,818 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:21:28,106 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:21:28,106 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:21:31,238 - main - INFO - Running all bots simultaneously...
2025-04-12 01:21:31,244 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:21:31,250 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:21:31,253 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:21:31,767 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:21:31,767 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:21:31,767 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:21:31,873 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:21:31,877 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:21:31,882 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:36:08,405 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:36:08,405 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:36:10,391 - main - INFO - Running all bots simultaneously...
2025-04-12 01:36:10,398 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:36:10,405 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:36:10,409 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:36:10,941 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:36:10,942 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:36:10,942 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:36:11,032 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:36:11,032 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:36:11,032 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:39:12,286 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:39:12,287 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:39:14,746 - main - INFO - Running all bots simultaneously...
2025-04-12 01:39:14,753 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:39:14,759 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:39:14,763 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:39:15,304 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:39:15,305 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:39:15,305 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:39:15,394 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:39:15,398 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:42:22,903 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:42:22,904 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 01:42:22,904 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-12 01:42:22,911 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:42:25,277 - main - INFO - Running all bots simultaneously...
2025-04-12 01:42:25,283 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:42:25,289 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:42:25,294 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:42:25,791 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:42:25,792 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:42:25,792 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:42:25,876 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:42:25,877 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:42:25,882 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:49:35,931 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:49:35,931 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:49:38,046 - main - INFO - Running all bots simultaneously...
2025-04-12 01:49:38,053 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:49:38,060 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:49:38,065 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:49:38,542 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:49:38,542 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:49:38,542 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:49:38,542 - run_all_bots - ERROR - Error running themethodbot: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)
2025-04-12 01:49:38,544 - run_all_bots - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\run_all_bots.py", line 22, in run_themethodbot
    from themethodbot import themethodbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\themethodbot.py", line 22, in <module>
    from themethodbot.common.bot import (
ImportError: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)

2025-04-12 01:49:38,596 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:49:38,608 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:50:06,031 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:50:06,032 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:50:08,590 - main - INFO - Running all bots simultaneously...
2025-04-12 01:50:08,596 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:50:08,603 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:50:08,607 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:50:09,040 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:50:09,041 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:50:09,041 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:50:09,041 - run_all_bots - ERROR - Error running themethodbot: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)
2025-04-12 01:50:09,042 - run_all_bots - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\run_all_bots.py", line 22, in run_themethodbot
    from themethodbot import themethodbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\themethodbot.py", line 22, in <module>
    from themethodbot.common.bot import (
ImportError: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)

2025-04-12 01:50:09,131 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:50:09,132 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:53:13,034 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:53:13,035 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:53:15,630 - main - INFO - Running all bots simultaneously...
2025-04-12 01:53:15,634 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:53:15,641 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:53:15,644 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:53:16,109 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:53:16,109 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:53:16,109 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:53:16,110 - run_all_bots - ERROR - Error running themethodbot: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)
2025-04-12 01:53:16,111 - run_all_bots - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\run_all_bots.py", line 22, in run_themethodbot
    from themethodbot import themethodbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\themethodbot.py", line 22, in <module>
    from themethodbot.common.bot import (
ImportError: cannot import name 'process_cart_items' from 'themethodbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\common\bot.py)

2025-04-12 01:53:16,180 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:53:16,184 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:55:11,124 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:55:11,125 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:56:26,373 - main - INFO - Running all bots simultaneously...
2025-04-12 01:56:26,378 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:56:26,383 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:56:26,386 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:56:26,866 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:56:26,866 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:56:26,866 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:56:26,868 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:56:26,870 - run_all_bots - ERROR - Error running themethodbot: cannot import name 'process_cart_items' from 'common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\common\check_group_order.py)
2025-04-12 01:56:26,871 - run_all_bots - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\run_all_bots.py", line 22, in run_themethodbot
    from themethodbot import themethodbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\themethodbot.py", line 30, in <module>
    from common.check_group_order import process_cart_items, calculate_fees, process_group_order
ImportError: cannot import name 'process_cart_items' from 'common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\common\check_group_order.py)

2025-04-12 01:56:26,934 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:56:26,934 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:58:06,589 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 01:58:06,590 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-12 01:58:06,591 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 01:58:09,076 - main - INFO - Running all bots simultaneously...
2025-04-12 01:58:09,082 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 01:58:09,088 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 01:58:09,092 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 01:58:09,577 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 01:58:09,577 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 01:58:09,578 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 01:58:09,643 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:58:09,643 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 01:58:09,669 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:01:23,722 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 02:01:23,722 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 02:01:26,265 - main - INFO - Running all bots simultaneously...
2025-04-12 02:01:26,271 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 02:01:26,278 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 02:01:26,281 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 02:01:26,776 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 02:01:26,776 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 02:01:26,777 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 02:01:26,832 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:01:26,838 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:01:26,855 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:05:12,149 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 02:05:12,150 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 02:05:14,664 - main - INFO - Running all bots simultaneously...
2025-04-12 02:05:14,669 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 02:05:14,674 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 02:05:14,678 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 02:05:15,148 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 02:05:15,148 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 02:05:15,148 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 02:05:15,215 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:05:15,217 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 02:05:15,238 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 14:32:09,168 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 14:32:09,176 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 14:32:09,197 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 14:32:22,215 - main - INFO - Running all bots simultaneously...
2025-04-12 14:32:22,225 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 14:32:22,236 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 14:32:22,241 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 14:32:22,814 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 14:32:22,814 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 14:32:22,814 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 14:32:22,959 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 14:32:22,959 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 14:32:22,960 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 18:47:32,318 - main - INFO - Running all bots simultaneously...
2025-04-12 18:47:32,326 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 18:47:32,337 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 18:47:32,342 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 18:47:33,368 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 18:47:33,369 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 18:47:33,369 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 18:47:33,512 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 18:47:33,512 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:42:48,992 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 19:42:48,995 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 19:42:55,448 - main - INFO - Running all bots simultaneously...
2025-04-12 19:42:55,454 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 19:42:55,461 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 19:42:55,464 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 19:42:56,201 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 19:42:56,202 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 19:42:56,202 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 19:42:56,337 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:42:56,337 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:42:56,337 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:49:21,555 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 19:49:21,555 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 19:49:26,081 - main - INFO - Running all bots simultaneously...
2025-04-12 19:49:26,088 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 19:49:26,094 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 19:49:26,098 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 19:49:26,602 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 19:49:26,602 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 19:49:26,602 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 19:49:26,687 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:49:26,690 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:49:26,697 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:52:35,892 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 19:52:35,892 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-12 19:52:35,894 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 19:52:56,621 - main - INFO - Running all bots simultaneously...
2025-04-12 19:52:56,627 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 19:52:56,633 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 19:52:56,635 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 19:52:57,113 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 19:52:57,113 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 19:52:57,114 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 19:52:57,190 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:52:57,196 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 19:52:57,196 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 20:15:42,779 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 20:15:42,780 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 20:15:42,781 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 20:15:46,402 - main - INFO - Running all bots simultaneously...
2025-04-12 20:15:46,409 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 20:15:46,416 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 20:15:46,420 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 20:15:47,015 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 20:15:47,016 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 20:15:47,016 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 20:15:47,127 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 20:15:47,127 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:12:08,692 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 21:12:08,696 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 21:12:14,710 - main - INFO - Running all bots simultaneously...
2025-04-12 21:12:14,717 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 21:12:14,725 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 21:12:14,729 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 21:12:15,318 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 21:12:15,318 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 21:12:15,318 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 21:12:15,424 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:12:15,424 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:34:32,177 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 21:34:32,177 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 21:34:39,560 - main - INFO - Running all bots simultaneously...
2025-04-12 21:34:39,566 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 21:34:39,572 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 21:34:39,576 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 21:34:40,059 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 21:34:40,060 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 21:34:40,060 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 21:34:40,133 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:34:40,146 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:34:40,147 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:47:40,211 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 21:47:40,212 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 21:47:44,762 - main - INFO - Running all bots simultaneously...
2025-04-12 21:47:44,773 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 21:47:44,784 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 21:47:44,790 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 21:47:45,549 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 21:47:45,549 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 21:47:45,549 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 21:47:45,676 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:47:45,676 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 21:47:45,683 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:02:03,524 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 22:02:03,526 - run_all_bots - INFO - Terminating budgetbot...
2025-04-12 22:02:15,219 - main - INFO - Running all bots simultaneously...
2025-04-12 22:02:15,314 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 22:02:15,482 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 22:02:15,494 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 22:02:33,831 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:02:37,659 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 22:02:37,749 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 22:02:37,749 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 22:02:44,661 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:02:46,240 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:19:15,532 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-12 22:19:15,537 - run_all_bots - INFO - All bots have been shut down.
2025-04-12 22:19:19,669 - main - INFO - Running all bots simultaneously...
2025-04-12 22:19:19,690 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-12 22:19:19,710 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-12 22:19:19,719 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-12 22:19:21,357 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-12 22:19:21,357 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-12 22:19:21,358 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-12 22:19:21,550 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:19:21,550 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-12 22:19:21,551 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 00:46:23,765 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 00:46:23,770 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 00:46:28,165 - main - INFO - Running all bots simultaneously...
2025-04-13 00:46:28,171 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 00:46:28,179 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 00:46:28,184 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 00:46:29,076 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 00:46:29,076 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 00:46:29,076 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 00:46:29,232 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 00:46:29,232 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 00:46:29,232 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:27:43,131 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:31:31,798 - main - INFO - Running all bots simultaneously...
2025-04-13 01:31:31,805 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:31:31,814 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:31:31,819 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:31:33,202 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:31:33,202 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:31:33,202 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:31:33,394 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:31:33,394 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:31:33,395 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:36:58,769 - main - INFO - Running all bots simultaneously...
2025-04-13 01:36:58,773 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:36:58,778 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:36:58,781 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:36:59,240 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:36:59,241 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:36:59,241 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:36:59,315 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:36:59,315 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:36:59,318 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:45:37,837 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 01:45:37,837 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-13 01:45:37,838 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:45:41,094 - main - INFO - Running all bots simultaneously...
2025-04-13 01:45:41,098 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:45:41,103 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:45:41,106 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:45:41,541 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:45:41,541 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:45:41,542 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:45:41,601 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:45:41,601 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:45:41,629 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:49:57,073 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 01:49:57,073 - run_all_bots - INFO - Terminating budgetbot...
2025-04-13 01:49:57,074 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-13 01:49:57,081 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:50:01,031 - main - INFO - Running all bots simultaneously...
2025-04-13 01:50:01,036 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:50:01,041 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:50:01,044 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:50:01,480 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:50:01,480 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:50:01,481 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:50:01,532 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:50:01,532 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:50:01,556 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:57:16,446 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 01:57:16,446 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:57:21,657 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 01:57:21,657 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:57:25,385 - main - INFO - Running all bots simultaneously...
2025-04-13 01:57:25,391 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:57:25,396 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:57:25,399 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:57:25,827 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:57:25,828 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:57:25,828 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:57:25,875 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:57:25,901 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:58:10,219 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 01:58:10,220 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 01:58:36,795 - main - INFO - Running all bots simultaneously...
2025-04-13 01:58:36,799 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 01:58:36,804 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 01:58:36,807 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 01:58:37,265 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 01:58:37,266 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 01:58:37,266 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 01:58:37,314 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 01:58:37,340 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:13:37,817 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 02:13:37,817 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 02:13:41,206 - main - INFO - Running all bots simultaneously...
2025-04-13 02:13:41,211 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 02:13:41,216 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 02:13:41,219 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 02:13:41,639 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 02:13:41,639 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 02:13:41,639 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 02:13:41,718 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:13:41,768 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:13:41,768 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:19:54,585 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 02:19:54,585 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 02:19:58,111 - main - INFO - Running all bots simultaneously...
2025-04-13 02:19:58,117 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 02:19:58,123 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 02:19:58,127 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 02:19:58,635 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 02:19:58,635 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 02:19:58,635 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 02:19:58,744 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:19:58,783 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:19:58,794 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:23:19,045 - main - INFO - Running all bots simultaneously...
2025-04-13 02:23:19,050 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 02:23:19,058 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 02:23:19,061 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 02:23:19,591 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 02:23:19,592 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 02:23:19,592 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 02:23:19,690 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:23:19,767 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:23:19,768 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:35:34,226 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 02:35:34,226 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 02:35:37,717 - main - INFO - Running all bots simultaneously...
2025-04-13 02:35:37,721 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 02:35:37,727 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 02:35:37,730 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 02:35:38,119 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 02:35:38,120 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 02:35:38,120 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 02:35:38,205 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:35:38,260 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 02:35:38,262 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 11:27:15,438 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 11:27:15,440 - run_all_bots - INFO - Terminating budgetbot...
2025-04-13 11:27:15,440 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-13 11:27:15,444 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 11:27:38,962 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 11:27:51,887 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 11:27:51,887 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 11:27:51,888 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 11:27:51,957 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 11:30:47,579 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 11:30:47,579 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 11:30:47,579 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 11:30:47,649 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 11:59:13,449 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 11:59:13,449 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 11:59:13,449 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 11:59:13,530 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 12:27:57,366 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 12:27:57,366 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 12:27:57,367 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 12:27:57,437 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 12:48:30,693 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 12:48:30,693 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 12:48:30,693 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 12:48:30,767 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 14:32:19,418 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 20:23:15,901 - main - INFO - Running all bots simultaneously...
2025-04-13 20:23:15,908 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-13 20:23:15,914 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-13 20:23:15,917 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-13 20:23:16,629 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 20:23:16,629 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 20:23:16,629 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 20:23:16,758 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 20:23:16,780 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 21:18:43,937 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 21:18:47,497 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-13 21:18:47,497 - run_all_bots - INFO - All bots have been shut down.
2025-04-13 21:19:02,777 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-13 21:19:02,777 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-13 21:19:02,777 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-13 21:19:02,845 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-13 21:19:12,858 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 10:53:56,778 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-15 10:53:56,778 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-15 10:53:56,778 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-15 10:53:56,966 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 12:14:02,410 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-15 12:14:02,410 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-15 12:14:02,410 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-15 12:14:02,540 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 12:41:20,540 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-15 12:41:20,540 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-15 12:41:20,540 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-15 12:41:20,666 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 13:03:26,417 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-15 13:03:26,417 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-15 13:03:26,417 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-15 13:03:26,505 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 13:05:44,364 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-15 13:05:44,364 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-15 13:05:44,364 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-15 13:05:44,448 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-15 13:07:56,907 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 14:03:25,100 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 14:03:25,100 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 14:03:25,100 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 14:03:25,233 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 14:52:17,160 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 14:52:17,160 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 14:52:17,161 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 14:52:17,259 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 14:59:00,749 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 14:59:00,750 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 14:59:00,750 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 14:59:00,829 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:05:51,326 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:05:51,327 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:05:51,327 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:05:51,421 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:19:08,867 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:19:08,868 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:19:08,868 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:19:09,024 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:35:07,714 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:35:07,714 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:35:07,714 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:35:07,799 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:36:18,583 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:40:34,533 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:40:34,533 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:40:34,533 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:40:34,628 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:42:20,330 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:42:20,330 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:42:20,331 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:42:20,416 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:43:30,322 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:43:30,322 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:43:30,322 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:43:30,422 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 15:58:37,803 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 15:58:37,803 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 15:58:37,803 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 15:58:37,888 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 17:04:51,821 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 17:04:55,399 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-17 17:04:55,399 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 17:04:55,399 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-17 17:04:55,472 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 17:13:24,742 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 18:17:35,002 - main - ERROR - Error running eatscheckerbot: cannot import name 'check_group_order' from 'eatscheckerbot.common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\check_group_order.py)
2025-04-17 18:17:35,004 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 32, in run_bot
    from eatscheckerbot import eatscheckerbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\eatscheckerbot.py", line 22, in <module>
    from eatscheckerbot.common.bot import (
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\bot.py", line 25, in <module>
    from eatscheckerbot.common.check_group_order import (
ImportError: cannot import name 'check_group_order' from 'eatscheckerbot.common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\check_group_order.py)

2025-04-17 18:17:38,193 - main - ERROR - Error running eatscheckerbot: cannot import name 'check_group_order' from 'eatscheckerbot.common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\check_group_order.py)
2025-04-17 18:17:38,195 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 32, in run_bot
    from eatscheckerbot import eatscheckerbot
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\eatscheckerbot.py", line 22, in <module>
    from eatscheckerbot.common.bot import (
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\bot.py", line 25, in <module>
    from eatscheckerbot.common.check_group_order import (
ImportError: cannot import name 'check_group_order' from 'eatscheckerbot.common.check_group_order' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\check_group_order.py)

2025-04-17 18:26:30,151 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 18:26:30,151 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 18:26:30,232 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 18:32:05,104 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 18:32:05,105 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 18:32:05,202 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 18:38:45,732 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 18:38:45,733 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 18:38:45,846 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 18:57:17,289 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 18:57:17,289 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 18:57:17,375 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 20:45:23,348 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 20:45:23,348 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 20:45:23,486 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 21:07:10,083 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 21:07:10,083 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 21:07:10,200 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-17 22:06:18,431 - eatscheckerbot.bot - INFO - UBER_COOKIE set: True
2025-04-17 22:06:18,431 - eatscheckerbot.bot - INFO - TOKEN_2 set: True
2025-04-17 22:06:18,515 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 13:41:47,725 - main - ERROR - Error running eatscheckerbot: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)
2025-04-18 13:41:47,726 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 32, in run_bot
    from eatscheckerbot import eatscheckerbot
ImportError: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)

2025-04-18 13:42:17,567 - main - ERROR - Error running eatscheckerbot: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)
2025-04-18 13:42:17,568 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 32, in run_bot
    from eatscheckerbot import eatscheckerbot
ImportError: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)

2025-04-18 14:43:13,323 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:04:04,085 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 15:04:04,085 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:04:04,085 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:04:04,220 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:06:24,630 - main - ERROR - Error running eatscheckerbot: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)
2025-04-18 15:06:24,631 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 32, in run_bot
    from eatscheckerbot import eatscheckerbot
ImportError: cannot import name 'eatscheckerbot' from 'eatscheckerbot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py)

2025-04-18 15:07:14,369 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 15:07:14,369 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:07:14,370 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:07:14,450 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:07:31,420 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 15:07:31,420 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:07:31,421 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:07:31,500 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:07:48,620 - main - INFO - Running all bots simultaneously...
2025-04-18 15:07:48,631 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 15:07:48,641 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 15:07:48,646 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 15:07:49,056 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 15:07:49,056 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:07:49,056 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:07:49,056 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 15:07:49,057 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:07:49,057 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:07:49,138 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:07:49,138 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:07:49,142 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:08:02,006 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 15:08:02,006 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 15:08:20,721 - main - INFO - Running all bots simultaneously...
2025-04-18 15:08:20,729 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 15:08:20,735 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 15:08:20,739 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 15:08:21,184 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 15:08:21,185 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:08:21,185 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:08:21,189 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 15:08:21,189 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 15:08:21,189 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 15:08:21,268 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:08:21,283 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:08:21,295 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 15:08:34,928 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 15:08:34,928 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 17:43:26,431 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 17:43:26,432 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 17:43:26,432 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 17:43:26,505 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 17:58:49,077 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 17:58:49,077 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 17:58:49,078 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 17:58:49,151 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:13:04,393 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:13:04,393 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:13:04,393 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:13:04,463 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:18:01,549 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:18:01,549 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:18:01,549 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:18:01,626 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:19:03,933 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:19:03,933 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:19:03,934 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:19:04,002 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:23:11,830 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:23:11,831 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:23:11,831 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:23:11,831 - main - ERROR - Error running eatscheckerbot: cannot import name 'nelo' from 'eatscheckerbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\bot.py)
2025-04-18 18:23:11,833 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\main.py", line 33, in run_bot
    from eatscheckerbot import bot, DISCORD_BOT_TOKEN
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\__init__.py", line 3, in <module>
    from .eatsbot import bot, DISCORD_BOT_TOKEN
  File "D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\eatsbot.py", line 22, in <module>
    from eatscheckerbot.common.bot import (
ImportError: cannot import name 'nelo' from 'eatscheckerbot.common.bot' (D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\common\bot.py)

2025-04-18 18:23:30,127 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:23:30,127 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:23:30,128 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:23:30,196 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:24:32,885 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:24:32,885 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:24:32,885 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:24:32,982 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:25:13,793 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:25:13,794 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:25:13,794 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:25:13,869 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:25:34,825 - main - INFO - Running all bots simultaneously...
2025-04-18 18:25:34,830 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 18:25:34,836 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 18:25:34,841 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 18:25:35,235 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 18:25:35,235 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:25:35,236 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:25:35,240 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:25:35,240 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:25:35,240 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:25:35,309 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:25:35,315 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:25:35,324 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:25:48,784 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 18:25:48,784 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-18 18:25:48,788 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 18:26:05,533 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:26:05,533 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:26:05,533 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:26:05,629 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:31:55,711 - main - INFO - Running all bots simultaneously...
2025-04-18 18:31:55,716 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 18:31:55,720 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 18:31:55,723 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 18:31:56,105 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 18:31:56,105 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:31:56,105 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:31:56,147 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:31:56,148 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:31:56,148 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:31:56,177 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:31:56,181 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:31:56,223 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:32:09,646 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 18:32:09,648 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-18 18:32:09,648 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 18:34:34,360 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:34:34,360 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:34:34,360 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:34:34,434 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:36:38,063 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:36:38,063 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:36:38,064 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:36:38,136 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 18:44:05,195 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 18:44:05,197 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 18:44:05,197 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 18:44:05,270 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:03,191 - main - INFO - Running all bots simultaneously...
2025-04-18 19:01:03,202 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 19:01:03,214 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 19:01:03,218 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 19:01:04,280 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:04,280 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:04,282 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:26,430 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 19:01:26,430 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 19:01:41,833 - main - INFO - Running all bots simultaneously...
2025-04-18 19:01:41,838 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 19:01:41,844 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 19:01:41,848 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 19:01:42,669 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:42,690 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:01:42,695 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:02:14,685 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-18 19:02:14,685 - run_all_bots - INFO - All bots have been shut down.
2025-04-18 19:03:12,428 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:04:04,354 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:04:49,653 - main - ERROR - Error running themethodbot: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)
2025-04-18 19:04:49,653 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\main.py", line 24, in run_bot
    from themethodbot import themethodbot_wrapper
ImportError: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)

2025-04-18 19:05:02,798 - main - ERROR - Error running themethodbot: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)
2025-04-18 19:05:02,799 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\main.py", line 24, in run_bot
    from themethodbot import themethodbot_wrapper
ImportError: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)

2025-04-18 19:06:01,095 - main - ERROR - Error running themethodbot: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)
2025-04-18 19:06:01,096 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\main.py", line 24, in run_bot
    from themethodbot import themethodbot_wrapper
ImportError: cannot import name 'themethodbot_wrapper' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)

2025-04-18 19:06:27,969 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:08:50,218 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:10:48,460 - main - ERROR - Error running themethodbot: cannot import name 'themethodbot_fixed' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)
2025-04-18 19:10:48,460 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\main.py", line 24, in run_bot
    from themethodbot import themethodbot_fixed as themethodbot
ImportError: cannot import name 'themethodbot_fixed' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)

2025-04-18 19:14:12,914 - main - ERROR - Error running themethodbot: cannot import name 'themethodbot_fixed' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)
2025-04-18 19:14:12,914 - main - ERROR - Traceback (most recent call last):
  File "D:\ryan4\Desktop\themethod\main.py", line 24, in run_bot
    from themethodbot import themethodbot_fixed as themethodbot
ImportError: cannot import name 'themethodbot_fixed' from 'themethodbot' (D:\ryan4\Desktop\themethod\themethodbot\__init__.py)

2025-04-18 19:16:15,141 - main - INFO - Running all bots simultaneously...
2025-04-18 19:16:15,148 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-18 19:16:15,153 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-18 19:16:15,155 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-18 19:16:15,575 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 19:16:15,575 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 19:16:15,575 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 19:16:15,576 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 19:16:15,576 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 19:16:15,576 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 19:16:15,658 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:16:15,661 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:16:15,664 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:17:07,739 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:17:30,405 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-18 19:17:30,405 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 19:17:30,405 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 19:17:30,472 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 19:17:41,377 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 19:17:41,377 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 19:17:41,377 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 19:17:41,451 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 21:12:16,087 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 21:12:16,087 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 21:12:16,087 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 21:12:16,194 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 22:34:24,928 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 22:34:24,928 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 22:34:24,928 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 22:34:25,008 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 22:48:13,569 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 22:48:13,569 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 22:48:13,569 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 22:48:13,651 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 22:52:27,486 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 22:52:27,486 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 22:52:27,487 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 22:52:27,563 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 22:59:11,650 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 22:59:11,650 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 22:59:11,650 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 22:59:11,728 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:00:08,036 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:00:08,036 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:00:08,036 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:00:08,114 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:08:38,186 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:08:38,187 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:08:38,187 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:08:38,264 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:09:45,880 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:09:45,880 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:09:45,881 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:09:45,964 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:13:00,745 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:13:00,745 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:13:00,745 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:13:00,820 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:15:32,746 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:15:32,746 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:15:32,746 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:15:32,822 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:18:12,304 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:18:12,304 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:18:12,304 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:18:12,374 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:26:02,918 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:26:02,918 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:26:02,919 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:26:03,003 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-18 23:28:40,420 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-18 23:28:40,420 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-18 23:28:40,420 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-18 23:28:40,491 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-19 11:54:54,973 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-04-19 11:54:54,973 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-19 11:54:54,973 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-19 11:54:55,045 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 13:06:04,411 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 13:06:04,411 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 13:06:04,411 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 13:06:04,534 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 13:14:03,092 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 13:14:03,092 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 13:14:03,092 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 13:14:03,164 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 13:29:25,990 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 13:29:25,990 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 13:29:25,990 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 13:29:26,083 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 13:30:43,058 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 13:30:43,058 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 13:30:43,058 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 13:30:43,134 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 13:32:23,079 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 13:32:23,079 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 13:32:23,079 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 13:32:23,179 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 14:56:02,007 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 14:56:02,007 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 14:56:02,007 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 14:56:02,090 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 15:35:37,924 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 15:35:37,924 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 15:35:37,925 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 15:35:38,034 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 17:25:06,564 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 17:25:06,564 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 17:25:06,564 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 17:25:06,689 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-25 17:36:10,720 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-25 17:36:10,720 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-25 17:36:10,720 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-25 17:36:10,798 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-26 20:17:43,091 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-04-26 20:17:43,091 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-04-26 20:17:43,091 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-04-26 20:17:43,239 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-04 13:26:58,134 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\themethodbot\.env
2025-05-04 13:26:58,134 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-05-04 13:26:58,134 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-04 13:26:58,405 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-04 13:28:12,729 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-04 13:28:17,702 - themethodbot.bot - INFO - Loaded local environment variables from D:\ryan4\Desktop\themethod\ordermanager\ordermanager\allbots\eatscheckerbot\.env
2025-05-04 13:28:17,702 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-05-04 13:28:17,702 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-04 13:28:17,803 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-12 13:36:11,968 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-12 13:36:23,260 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-12 13:36:23,260 - themethodbot.bot - INFO - UBER_COOKIE set: True
2025-05-12 13:36:23,261 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-12 13:36:23,354 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-18 20:01:38,213 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-18 20:01:38,214 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-18 20:01:38,214 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-18 20:01:38,353 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-18 20:01:44,442 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-19 20:50:39,468 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-19 20:50:52,263 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-19 20:50:52,263 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-19 20:50:52,264 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-19 20:50:52,395 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 20:13:01,314 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 20:13:01,315 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 20:13:01,315 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 20:13:01,446 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 20:22:09,783 - main - ERROR - Error running themethodbot: No module named 'psytil'
2025-05-22 20:22:09,785 - main - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\allbots\allbots\main.py", line 25, in run_bot
    from themethodbot import themethodbot
  File "C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\themethodbot.py", line 16, in <module>
    import psytil
ModuleNotFoundError: No module named 'psytil'

2025-05-22 20:35:00,978 - main - ERROR - Error running themethodbot: No module named 'psytil'
2025-05-22 20:35:00,979 - main - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\allbots\allbots\main.py", line 25, in run_bot
    from themethodbot import themethodbot
  File "C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\themethodbot.py", line 16, in <module>
    import psytil
ModuleNotFoundError: No module named 'psytil'

2025-05-22 20:35:10,934 - main - ERROR - Error running themethodbot: No module named 'psytil'
2025-05-22 20:35:10,936 - main - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\allbots\allbots\main.py", line 25, in run_bot
    from themethodbot import themethodbot
  File "C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\themethodbot.py", line 16, in <module>
    import psytil
ModuleNotFoundError: No module named 'psytil'

2025-05-22 20:35:58,307 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 20:35:58,307 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 20:35:58,307 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 20:35:58,436 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 20:36:18,333 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 20:36:18,333 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 20:36:18,334 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 20:36:18,430 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 21:35:15,413 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 21:35:15,414 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 21:35:15,414 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 21:35:15,520 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 21:37:36,129 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 21:37:36,130 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 21:37:36,130 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 21:37:36,219 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 21:45:50,028 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-22 21:45:50,028 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-22 21:45:50,029 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-22 21:45:50,186 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-22 22:13:38,790 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-23 21:54:44,977 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-26 15:42:23,878 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-26 15:42:29,031 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-26 15:42:29,032 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-26 15:42:29,032 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-26 15:42:29,140 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-27 11:26:14,522 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-05-27 11:26:14,522 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-05-27 11:26:14,523 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-05-27 11:26:14,619 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-05-27 23:10:02,073 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-06-02 14:02:36,703 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-06-02 14:02:36,704 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-06-02 14:02:36,704 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-06-02 14:02:36,822 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-06-04 11:42:43,051 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-06-04 11:42:43,052 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-06-04 11:42:43,052 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-06-04 11:42:43,163 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-06-21 18:20:34,704 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-06-21 18:20:34,704 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-06-21 18:20:34,705 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-06-21 18:20:34,814 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-07-08 20:21:25,384 - themethodbot.bot - INFO - Loaded local environment variables from C:\Users\<USER>\Downloads\allbots\allbots\themethodbot\.env
2025-07-08 20:21:25,388 - themethodbot.bot - INFO - UBER_COOKIE set: False
2025-07-08 20:21:25,388 - themethodbot.bot - INFO - TOKEN_2 set: True
2025-07-08 20:21:25,777 - check_group_order - DEBUG - Logging initialized in check_group_order.py
