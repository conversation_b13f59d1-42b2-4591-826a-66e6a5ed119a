#!/usr/bin/env python3
"""
Test script to verify the enhanced restaurant field formatting
in the themethodbot order summary embeds.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from themethodbot.embed_templates import create_order_summary_embed, create_latestsummary_embed, format_store_hours

def test_format_store_hours():
    """Test the format_store_hours function with various inputs."""
    print("=== Testing format_store_hours function ===")
    
    # Test case 1: Store is open with hours
    store_info_1 = {
        'is_open': True,
        'hours': 'Open until 11:00 PM'
    }
    result_1 = format_store_hours(store_info_1)
    print(f"Test 1 - Open with hours: '{result_1}'")
    
    # Test case 2: Store is closed
    store_info_2 = {
        'is_open': False,
        'hours': 'Closed'
    }
    result_2 = format_store_hours(store_info_2)
    print(f"Test 2 - Closed: '{result_2}'")
    
    # Test case 3: Status string
    store_info_3 = {
        'status': 'open',
        'hours': 'Open 24 hours'
    }
    result_3 = format_store_hours(store_info_3)
    print(f"Test 3 - Status string: '{result_3}'")
    
    # Test case 4: No hours info
    store_info_4 = {
        'is_open': True
    }
    result_4 = format_store_hours(store_info_4)
    print(f"Test 4 - No hours info: '{result_4}'")
    
    # Test case 5: Empty store info
    store_info_5 = {}
    result_5 = format_store_hours(store_info_5)
    print(f"Test 5 - Empty info: '{result_5}'")

def test_enhanced_restaurant_field():
    """Test the enhanced restaurant field in embeds."""
    print("\n=== Testing Enhanced Restaurant Field ===")
    
    # Test data with comprehensive store information
    test_result = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {
            'name': "McDonald's",
            'delivery_time': '25-35',
            'is_open': True,
            'hours': 'Open until 11:00 PM',
            'banner_image': 'https://example.com/banner.jpg'
        },
        'store_url': 'https://www.ubereats.com/store/mcdonalds-test',
        'location': {
            'address': '123 Main St',
            'city': 'New York',
            'state': 'NY',
            'zipcode': '10001'
        }
    }
    
    test_cart_items = [
        'Big Mac x1 ($5.99)',
        'Large Fries x1 ($2.99)',
        'Coca-Cola x1 ($1.99)'
    ]
    
    test_fee_calculations = {
        'subtotal': 10.97,
        'is_cad': False
    }
    
    # Test order summary embed
    print("Creating order summary embed...")
    try:
        summary_embed = create_order_summary_embed(test_result, test_cart_items, test_fee_calculations)
        print("✅ Order summary embed created successfully")
        
        # Print the restaurant field content
        for field in summary_embed.fields:
            if 'Restaurant' in field.name:
                print(f"Restaurant field content:\n{field.value}")
                break
        
    except Exception as e:
        print(f"❌ Error creating order summary embed: {e}")
    
    # Test latestsummary embed
    print("\nCreating latestsummary embed...")
    try:
        latest_embed = create_latestsummary_embed(test_result, test_cart_items, test_fee_calculations)
        print("✅ Latest summary embed created successfully")
        
        # Print the restaurant field content
        for field in latest_embed.fields:
            if 'Restaurant' in field.name:
                print(f"Restaurant field content:\n{field.value}")
                break
                
    except Exception as e:
        print(f"❌ Error creating latestsummary embed: {e}")

def test_edge_cases():
    """Test edge cases for the enhanced restaurant field."""
    print("\n=== Testing Edge Cases ===")
    
    # Test case 1: No store info
    test_result_1 = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {},
        'location': {'address': '123 Main St'}
    }
    
    # Test case 2: Store name only
    test_result_2 = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {
            'name': "Pizza Hut"
        },
        'location': {'address': '123 Main St'}
    }
    
    # Test case 3: Store name with delivery time only
    test_result_3 = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {
            'name': "Subway",
            'delivery_time': '15-25 min'
        },
        'store_url': 'https://www.ubereats.com/store/subway-test',
        'location': {'address': '123 Main St'}
    }
    
    test_cases = [
        ("No store info", test_result_1),
        ("Store name only", test_result_2),
        ("Store name + delivery time", test_result_3)
    ]
    
    for case_name, test_data in test_cases:
        print(f"\nTesting: {case_name}")
        try:
            embed = create_order_summary_embed(test_data, [], {'subtotal': 0, 'is_cad': False})
            
            # Check if restaurant field exists
            restaurant_field = None
            for field in embed.fields:
                if 'Restaurant' in field.name:
                    restaurant_field = field
                    break
            
            if restaurant_field:
                print(f"✅ Restaurant field found: {restaurant_field.value}")
            else:
                print("ℹ️ No restaurant field (expected for empty store info)")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("Testing Enhanced Restaurant Field Implementation")
    print("=" * 50)
    
    test_format_store_hours()
    test_enhanced_restaurant_field()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("Testing complete!")
