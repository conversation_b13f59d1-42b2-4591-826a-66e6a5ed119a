import discord
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('themethodbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="<:padlock:1360153078149677207> Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360138365621506048/padlock.gif?ex=67fa0710&is=67f8b590&hm=55b33ef59bdd15b35e8f627407e05a63340cae2955c2772389a47f5ea48c9be6&=")  # Lock icon

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="<:search:1360154069028835410> Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Search/store icon

    # Add spacing to the description
    embed.description += "\n\n"

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open the Promo Link",
        value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Open the Promo Link Again in a NEW tab",
        value="Open [**the same link**](https://tinyurl.com/TheMethodUE) in a second tab",
        inline=False
    )

    # Step 4
    embed.add_field(
        name="🔹 Step 4: It should say \"$25 off\", now you can Browse Eligible Restaurants",
        value="You'll now see all restaurants eligible for our promo!",
        inline=False
    )

    # Add a footer with a tip
    embed.set_footer(text="It should look like the image below")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="<:burger:1360153508929732617> The Method Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Simple description with just the group order link
    embed.description = f"**<:link:1360154729002565662> [Group Order Link]({group_link})**\n\n"

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="<:placeholder:1360153497869488137> Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="<:store:1360153496376315984> Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"<:shoppingcart:1360153495155642459> Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # New pricing structure with $25 discount
    embed.add_field(
        name="<:moneybag:1360153494031564840> Price Breakdown",
        value=f"Original Subtotal: `{currency}{fee_calculations.get('subtotal', 0):.2f}`\n" +
              f"Discount: `-{currency}{fee_calculations.get('discount_amount', 25):.2f}`\n" +
              f"Discounted Subtotal: `{currency}{fee_calculations.get('discounted_subtotal', 0):.2f}`\n" +
              f"You Save: `{currency}{fee_calculations.get('savings', 0):.2f}`",
        inline=True
    )

    # Fees breakdown - detailed format
    fees_str = ""

    # Get all fee components from the calculations
    overflow_fee = fee_calculations.get('overflow_fee', 0)
    service_fee = fee_calculations.get('service_fee', 0)
    taxes = fee_calculations.get('taxes', 0)
    method_fee = 9.00  # Fixed method fee
    uber_one_discount = fee_calculations.get('uber_one_discount', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)

    # Check if service fee and taxes are both $0, which indicates "You pay for your portion" setting
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        fees_str = "**Unable to fetch pricing**\nGroup order is set to \"You pay for your portion\""
    else:
        # Build the detailed fees breakdown
        fees_str = f"Overflow Fee: `{currency}{overflow_fee:.2f}`\n"
        fees_str += f"Service Fee: `{currency}{service_fee:.2f}`\n"
        fees_str += f"Taxes: `{currency}{taxes:.2f}`\n"
        fees_str += f"Method Fee: `{currency}{method_fee:.2f}`\n"
        if uber_one_discount > 0:
            fees_str += f"Uber One Discount: `-{currency}{uber_one_discount:.2f}`"

    embed.add_field(
        name="🪙 Fees & Taxes",
        value=fees_str,
        inline=False
    )

    # Calculate and display final total with emphasis - using our new pricing model
    discounted_subtotal = fee_calculations.get('discounted_subtotal', 0)
    method_fee = fee_calculations.get('method_fee', 10.00)
    taxes = fee_calculations.get('taxes', 0)
    overflow_fee = fee_calculations.get('overflow_fee', 0)
    final_total = round(discounted_subtotal + taxes + method_fee + overflow_fee, 2)

    # Calculate the before amount (original subtotal)
    original_subtotal = fee_calculations.get('subtotal', 0)

    # Sum all components for the before amount
    before_amount = original_subtotal

    # Only show final total if it's not zero and we have valid fee data
    service_fee = fee_calculations.get('service_fee', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)
    taxes = fee_calculations.get('taxes', 0)

    if final_total > 0 and not (service_fee == 0 and taxes == 0 and ca_driver_benefit == 0):
        embed.add_field(
            name="<:cash:1360153490630119525> Estimated Final Total (Tip not included)",
            value=f"`{currency}{before_amount:.2f}` ➡️ **`{currency}{final_total:.2f}`**",
            inline=False
        )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Food/order icon

    # Add footer with helpful information - only show estimate warning if we have valid pricing
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.set_footer(text="The Method | Order Summary")
    else:
        embed.set_footer(text="⚠️ This is an ESTIMATE. Please wait for a Chef to confirm your final prices | The Method")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="<:warning:1360153488629432351> Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="<:warning:1360153488629432351> Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="<:cancel:1360154555295207596> Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add troubleshooting steps
    embed.add_field(
        name="<:search:1360154069028835410> Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139294890655936/alert_1.gif?ex=67fa07ee&is=67f8b66e&hm=3a983f0f8ae1980bc4abe3fc77efa37b61c8fb862570944ce1ca4290ab3e4b78&=")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139708234993674/loading.gif?ex=67fa0850&is=67f8b6d0&hm=a4286ff0bd61b871ec51cb03ce8a589cd24d93f101bb10fa414567816d9c4a57&=")  # Loading animation

    return embed

def create_latestsummary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a special embed specifically for the /latestsummary command."""
    embed = discord.Embed(
        title="<:burger:1360153508929732617> The Method Order Summary",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple - different color from regular summary
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    if group_link != 'Not available':
        embed.description = f"<:link:1360154729002565662> **[Group Order Link]({group_link})**\n\n"
    else:
        embed.description = ""

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = ""
        for item in items_to_show:
            items_str += f"╰・ *{item}*\n"

        if remaining_items > 0:
            items_str += f"╰・ *... and {remaining_items} more items*"

        embed.add_field(
            name=f"<:shoppingcart:1360153495155642459> Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # New pricing structure with $25 discount
    embed.add_field(
        name="<:moneybag:1360153494031564840> Price Breakdown",
        value=f"Original Subtotal: `{currency}{fee_calculations.get('subtotal', 0):.2f}`\n" +
              f"Discount: `-{currency}{fee_calculations.get('discount_amount', 25):.2f}`\n" +
              f"Discounted Subtotal: `{currency}{fee_calculations.get('discounted_subtotal', 0):.2f}`\n" +
              f"You Save: `{currency}{fee_calculations.get('savings', 0):.2f}`",
        inline=True
    )

    # Detailed fees breakdown for latestsummary
    overflow_fee = fee_calculations.get('overflow_fee', 0)
    service_fee = fee_calculations.get('service_fee', 0)
    taxes = fee_calculations.get('taxes', 0)
    method_fee = 9.00  # Fixed method fee
    uber_one_discount = fee_calculations.get('uber_one_discount', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)

    # Check if service fee and taxes are both $0, which indicates "You pay for your portion" setting
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.add_field(
            name="🪙 Fees & Taxes",
            value="**Unable to fetch pricing**\nGroup order is set to \"You pay for your portion\"",
            inline=False
        )
    else:
        # Build the detailed fees breakdown
        fees_str = f"Overflow Fee: `{currency}{overflow_fee:.2f}`\n"
        fees_str += f"Service Fee: `{currency}{service_fee:.2f}`\n"
        fees_str += f"Taxes: `{currency}{taxes:.2f}`\n"
        fees_str += f"Method Fee: `{currency}{method_fee:.2f}`\n"
        if uber_one_discount > 0:
            fees_str += f"Uber One Discount: `-{currency}{uber_one_discount:.2f}`"

        embed.add_field(
            name="🪙 Fees & Taxes",
            value=fees_str,
            inline=False
        )

    # Use the pre-calculated final_total from fee_calculations
    # This ensures we use the exact value calculated in latestsummary.py
    final_total = fee_calculations.get('final_total', 0)

    # Calculate the before amount (original subtotal)
    original_subtotal = fee_calculations.get('subtotal', 0)
    before_amount = original_subtotal

    # Only show final total if it's not zero and we have valid fee data
    if final_total > 0 and not (service_fee == 0 and taxes == 0 and ca_driver_benefit == 0):
        embed.add_field(
            name="<:cash:1360153490630119525> Estimated Final Total (Tip not included)",
            value=f"`{currency}{before_amount:.2f}` ➡️ **`{currency}{final_total:.2f}`**",
            inline=False
        )

    # Set a different thumbnail for latestsummary
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")

    # No warning message in the footer for latestsummary
    embed.set_footer(text="The Method | Order Summary")

    return embed
