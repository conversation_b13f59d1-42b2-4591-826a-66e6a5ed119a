import discord
import logging
import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger('themethodbot.embeds')

def format_store_hours(store_info: Dict[str, Any]) -> str:
    """Format store hours information for display."""
    # Check for operating status first
    if store_info.get('is_open') is True:
        status = "Currently Open"
    elif store_info.get('is_open') is False:
        status = "Currently Closed"
    elif store_info.get('status') == 'open':
        status = "Currently Open"
    elif store_info.get('status') == 'closed':
        status = "Currently Closed"
    elif store_info.get('operating_status'):
        status = store_info['operating_status'].title()
    else:
        status = None

    # Try to extract hours information
    hours_info = store_info.get('hours') or store_info.get('operatingHours') or store_info.get('storeHours')

    if hours_info:
        # If hours_info is a string, return it directly
        if isinstance(hours_info, str):
            return f"{status} | {hours_info}" if status else hours_info

        # If hours_info is a dict, try to extract today's hours
        elif isinstance(hours_info, dict):
            today = datetime.datetime.now().strftime('%A').lower()
            today_hours = hours_info.get(today) or hours_info.get('today')

            if today_hours:
                if isinstance(today_hours, dict):
                    open_time = today_hours.get('open')
                    close_time = today_hours.get('close')
                    if open_time and close_time:
                        hours_str = f"Open until {close_time}"
                        return f"{status} | {hours_str}" if status else hours_str
                elif isinstance(today_hours, str):
                    return f"{status} | {today_hours}" if status else today_hours

    # Return just the status if no hours info available
    return status if status else "Hours not available"

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="<:padlock:1360153078149677207> Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360138365621506048/padlock.gif?ex=67fa0710&is=67f8b590&hm=55b33ef59bdd15b35e8f627407e05a63340cae2955c2772389a47f5ea48c9be6&=")  # Lock icon

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="<:search:1360154069028835410> Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Search/store icon

    # Add spacing to the description
    embed.description += "\n\n"

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open the Promo Link",
        value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Open the Promo Link Again in a NEW tab",
        value="Open [**the same link**](https://tinyurl.com/TheMethodUE) in a second tab",
        inline=False
    )

    # Step 4
    embed.add_field(
        name="🔹 Step 4: It should say \"$25 off\", now you can Browse Eligible Restaurants",
        value="You'll now see all restaurants eligible for our promo!",
        inline=False
    )

    # Add a footer with a tip
    embed.set_footer(text="It should look like the image below")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="<:burger:1360153508929732617> The Method Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Simple description with just the group order link
    embed.description = f"**<:link:1360154729002565662> [Group Order Link]({group_link})**\n\n"

    # Add enhanced store information if available
    store_info = result.get('store_info', {})
    if store_info.get('name'):
        restaurant_value = f"**{store_info['name']}**\n"

        # Add delivery time and store hours on the same line
        time_info_parts = []

        # Add delivery time if available
        delivery_time = store_info.get('delivery_time') or store_info.get('estimated_delivery_time')
        if delivery_time:
            # Clean up delivery time format
            if isinstance(delivery_time, str):
                if not delivery_time.endswith('min'):
                    delivery_time = f"{delivery_time} min"
                time_info_parts.append(f"⏰ Delivery: {delivery_time}")

        # Add store hours/status
        hours_info = format_store_hours(store_info)
        if hours_info and hours_info != "Hours not available":
            time_info_parts.append(hours_info)

        # Combine time information
        if time_info_parts:
            restaurant_value += " | ".join(time_info_parts) + "\n"

        # Add Uber Eats link if store URL is available
        store_url = result.get('store_url')
        if store_url:
            restaurant_value += f"🔗 [View on Uber Eats]({store_url})"

        embed.add_field(
            name="<:promotion:1360153519415361546> Restaurant",
            value=restaurant_value,
            inline=False
        )

    # Set store banner image if available
    if store_info.get('banner_image'):
        embed.set_image(url=store_info['banner_image'])

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="<:placeholder:1360153497869488137> Delivery Location",
        value=location_str,
        inline=False
    )

    # Store URL is now handled in the enhanced restaurant field above

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"<:shoppingcart:1360153495155642459> Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Simple subtotal display only
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)

    embed.add_field(
        name="<:moneybag:1360153494031564840> Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Food/order icon

    # Simple footer without warning
    embed.set_footer(text="The Method | Order Summary")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 20.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="<:warning:1360153488629432351> Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="<:warning:1360153488629432351> Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="<:cancel:1360154555295207596> Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add troubleshooting steps
    embed.add_field(
        name="<:search:1360154069028835410> Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139294890655936/alert_1.gif?ex=67fa07ee&is=67f8b66e&hm=3a983f0f8ae1980bc4abe3fc77efa37b61c8fb862570944ce1ca4290ab3e4b78&=")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139708234993674/loading.gif?ex=67fa0850&is=67f8b6d0&hm=a4286ff0bd61b871ec51cb03ce8a589cd24d93f101bb10fa414567816d9c4a57&=")  # Loading animation

    return embed

def create_latestsummary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a special embed specifically for the /latestsummary command."""
    embed = discord.Embed(
        title="<:burger:1360153508929732617> The Method Order Summary",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple - different color from regular summary
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    if group_link != 'Not available':
        embed.description = f"<:link:1360154729002565662> **[Group Order Link]({group_link})**\n\n"
    else:
        embed.description = ""

    # Add enhanced store information if available
    store_info = result.get('store_info', {})
    if store_info.get('name'):
        restaurant_value = f"**{store_info['name']}**\n"

        # Add delivery time and store hours on the same line
        time_info_parts = []

        # Add delivery time if available
        delivery_time = store_info.get('delivery_time') or store_info.get('estimated_delivery_time')
        if delivery_time:
            # Clean up delivery time format
            if isinstance(delivery_time, str):
                if not delivery_time.endswith('min'):
                    delivery_time = f"{delivery_time} min"
                time_info_parts.append(f"⏰ Delivery: {delivery_time}")

        # Add store hours/status
        hours_info = format_store_hours(store_info)
        if hours_info and hours_info != "Hours not available":
            time_info_parts.append(hours_info)

        # Combine time information
        if time_info_parts:
            restaurant_value += " | ".join(time_info_parts) + "\n"

        # Add Uber Eats link if store URL is available
        store_url = result.get('store_url')
        if store_url:
            restaurant_value += f"🔗 [View on Uber Eats]({store_url})"

        embed.add_field(
            name="<:promotion:1360153519415361546> Restaurant",
            value=restaurant_value,
            inline=False
        )

    # Set store banner image if available
    if store_info.get('banner_image'):
        embed.set_image(url=store_info['banner_image'])

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = ""
        for item in items_to_show:
            items_str += f"╰・ *{item}*\n"

        if remaining_items > 0:
            items_str += f"╰・ *... and {remaining_items} more items*"

        embed.add_field(
            name=f"<:shoppingcart:1360153495155642459> Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Simple subtotal display only
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)

    embed.add_field(
        name="<:moneybag:1360153494031564840> Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    # Set a different thumbnail for latestsummary
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")

    # No warning message in the footer for latestsummary
    embed.set_footer(text="The Method | Order Summary")

    return embed
