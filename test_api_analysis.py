#!/usr/bin/env python3
"""
Test script to analyze Uber Eats group order API responses
and identify additional data that can be extracted for embeds.
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from common.check_group_order import process_group_order, make_api_request, get_order_details_from_data
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging for detailed analysis
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('api_analysis.log', mode='w', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def analyze_group_order_api(group_order_url):
    """
    Analyze the API response from a group order to identify additional data fields.
    """
    logger.info(f"Analyzing group order: {group_order_url}")
    
    try:
        # Process the group order and capture detailed response
        result = await process_group_order(group_order_url)
        
        if result:
            logger.info("=== BASIC EXTRACTED DATA ===")
            logger.info(json.dumps(result, indent=2, default=str))
            
            # Now let's make direct API calls to get more detailed responses
            await analyze_detailed_api_responses(group_order_url)
            
        else:
            logger.error("Failed to process group order")
            
    except Exception as e:
        logger.error(f"Error analyzing group order: {e}")
        logger.exception("Full exception:")

async def analyze_detailed_api_responses(group_order_url):
    """
    Make detailed API calls to analyze what additional data is available.
    """
    logger.info("=== DETAILED API ANALYSIS ===")
    
    # Extract group UUID from URL
    import re
    uuid_match = re.search(r'/group-orders/([a-f0-9-]+)', group_order_url)
    if not uuid_match:
        logger.error("Could not extract group UUID from URL")
        return
    
    group_uuid = uuid_match.group(1)
    logger.info(f"Group UUID: {group_uuid}")
    
    # Create session with proper headers
    timeout = aiohttp.ClientTimeout(total=30)
    connector = aiohttp.TCPConnector(ssl=False)
    
    headers = {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        "cookie": os.getenv('25_Promo', ''),
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "x-csrf-token": "x",
        "origin": "https://www.ubereats.com",
        "referer": "https://www.ubereats.com/",
    }
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=headers
    ) as session:
        
        # 1. Get draft order data (main data source)
        logger.info("=== 1. DRAFT ORDER DATA ===")
        draft_payload = {"draftOrderUUID": group_uuid}
        draft_result = await make_api_request(session, "getDraftOrderByUuidV2", draft_payload)
        
        if draft_result.get('status') == 200:
            draft_data = draft_result.get('data', {}).get('data', {})
            logger.info("Draft order data structure:")
            logger.info(json.dumps(draft_data, indent=2, default=str))
            
            # Analyze store information
            await analyze_store_data(session, draft_data)
            
        # 2. Join the group order to get member data
        logger.info("=== 2. JOIN GROUP ORDER ===")
        join_payload = {
            "draftOrderUuid": group_uuid,
            "nickname": "APIAnalyzer"
        }
        join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload)
        
        if join_result.get('status') == 200:
            join_data = join_result.get('data', {}).get('data', {})
            logger.info("Join data structure:")
            logger.info(json.dumps(join_data, indent=2, default=str))

async def analyze_store_data(session, draft_data):
    """
    Analyze store-specific data that can be extracted.
    """
    logger.info("=== STORE DATA ANALYSIS ===")
    
    store_uuid = draft_data.get('storeUuid')
    if not store_uuid:
        logger.error("No store UUID found")
        return
    
    logger.info(f"Store UUID: {store_uuid}")
    
    # Try to get store information
    try:
        # Check if there's store info in the draft data
        if 'store' in draft_data:
            store_info = draft_data['store']
            logger.info("Store information found in draft data:")
            logger.info(json.dumps(store_info, indent=2, default=str))
        
        # Look for any image URLs or store metadata
        logger.info("=== SEARCHING FOR STORE IMAGES AND METADATA ===")
        
        def find_image_urls(data, path=""):
            """Recursively find all image URLs in the data."""
            images = []
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    if isinstance(value, str) and any(ext in value.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp', 'image']):
                        images.append(f"{current_path}: {value}")
                    elif isinstance(value, (dict, list)):
                        images.extend(find_image_urls(value, current_path))
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    images.extend(find_image_urls(item, current_path))
            return images
        
        def find_store_metadata(data, path=""):
            """Find store-related metadata."""
            metadata = []
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(term in key.lower() for term in ['store', 'restaurant', 'name', 'title', 'rating', 'cuisine', 'delivery', 'eta', 'time']):
                        if isinstance(value, (str, int, float, bool)):
                            metadata.append(f"{current_path}: {value}")
                    if isinstance(value, (dict, list)):
                        metadata.extend(find_store_metadata(value, current_path))
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    metadata.extend(find_store_metadata(item, current_path))
            return metadata
        
        images = find_image_urls(draft_data)
        metadata = find_store_metadata(draft_data)
        
        logger.info("=== FOUND IMAGE URLS ===")
        for img in images:
            logger.info(img)
        
        logger.info("=== FOUND STORE METADATA ===")
        for meta in metadata:
            logger.info(meta)
            
    except Exception as e:
        logger.error(f"Error analyzing store data: {e}")

async def main():
    """Main function to run the API analysis."""
    # Test with the provided group order link
    test_url = "https://eats.uber.com/group-orders/11fc4eba-6863-4446-9900-aa50e502f293/join?source=quickActionCopy"
    
    logger.info("Starting Uber Eats API Analysis")
    logger.info("=" * 50)
    
    await analyze_group_order_api(test_url)
    
    logger.info("=" * 50)
    logger.info("Analysis complete. Check api_analysis.log for detailed output.")

if __name__ == "__main__":
    asyncio.run(main())
