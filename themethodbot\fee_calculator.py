import logging
from typing import Dict, Any

logger = logging.getLogger('themethodbot.fee_calculator')

def calculate_fees_without_delivery(fees_data: Dict[str, Any], subtotal: float, is_cad: bool = False) -> Dict[str, Any]:
    """Calculate all fee-related values, excluding delivery fee."""
    # Always use the provided subtotal parameter
    actual_subtotal = subtotal

    # NEW PRICING MODEL: $25 discount with overflow fee and $10 service fee
    # Calculate discount amount (fixed $25)
    discount_amount = 25.00

    # Calculate discounted subtotal (subtract $25)
    discounted_subtotal = round(actual_subtotal - discount_amount, 2)

    # Calculate overflow fee (when discounted subtotal is positive)
    overflow_fee = round(max(0, discounted_subtotal), 2)

    # Set discounted subtotal to 0 if it was negative
    if discounted_subtotal < 0:
        discounted_subtotal = 0

    # Calculate savings (always $25 unless subtotal is less than $25)
    savings = round(min(actual_subtotal, discount_amount), 2)

    # Set Method fee (increased from $9 to $10)
    method_fee = 10.00

    # Extract fees from fees_data (for compatibility)
    service_fee = float(fees_data.get('service_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Calculate total fees (tax + method fee + overflow fee)
    total_fees = round(taxes + method_fee + overflow_fee, 2)
    final_fees = total_fees

    # Calculate final total (discounted subtotal + tax + method fee + overflow fee)
    final_total = round(discounted_subtotal + taxes + method_fee + overflow_fee, 2)

    # Return all calculated values
    return {
        'subtotal': actual_subtotal,
        'discounted_subtotal': discounted_subtotal,
        'discount_amount': discount_amount,
        'savings': savings,
        'overflow_fee': overflow_fee,
        'service_fee': service_fee,
        'delivery_fee': 0,  # Excluded
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'uber_one_discount': uber_one_discount,
        'total_fees': total_fees,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad,
        'method_fee': method_fee      # Add method fee for display
    }
