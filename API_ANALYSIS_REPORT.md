# Uber Eats Group Order API Analysis Report

## Executive Summary

This report analyzes the Uber Eats group order API endpoints used in `common/check_group_order.py` and documents the additional data that can be extracted to enhance order summary embeds.

## Current API Endpoints

### 1. **getDraftOrderByUuidV2**
- **Purpose**: Retrieves detailed group order information
- **Payload**: `{"draftOrderUUID": group_uuid}`
- **Primary Data Source**: Main source for order details, store info, and cart items

### 2. **getCheckoutPresentationV1** 
- **Purpose**: Gets pricing and fee information
- **Payload**: Complex payload with cart and store UUIDs
- **Data**: Fee breakdowns, promotions, pricing calculations

### 3. **getStoreV1**
- **Purpose**: Retrieves store-specific information
- **Payload**: `{"storeUuid": store_uuid}`
- **Data**: Store metadata, URLs, slugs

## Implemented Enhancements

### ✅ **Store Information Extraction**

**Added to `get_order_details_from_data()` function:**
```python
# Extract store information from the API response
store_info = {}

# Try to get store name from various locations in the response
store_name = None
if 'store' in data and data['store']:
    store_data = data['store']
    store_name = store_data.get('title') or store_data.get('name')
    
    # Extract store image/banner if available
    if 'heroImageUrl' in store_data:
        store_info['banner_image'] = store_data['heroImageUrl']
    elif 'imageUrl' in store_data:
        store_info['banner_image'] = store_data['imageUrl']
    
    # Extract additional store metadata
    if 'rating' in store_data:
        store_info['rating'] = store_data['rating']
    if 'cuisineType' in store_data:
        store_info['cuisine_type'] = store_data['cuisineType']
    if 'deliveryFee' in store_data:
        store_info['delivery_fee'] = store_data['deliveryFee']
    if 'estimatedDeliveryTime' in store_data:
        store_info['estimated_delivery_time'] = store_data['estimatedDeliveryTime']
```

### ✅ **Enhanced Embed Templates**

**Added to both `create_order_summary_embed()` and `create_latestsummary_embed()`:**
```python
# Add store information if available
store_info = result.get('store_info', {})
if store_info.get('name'):
    embed.add_field(
        name="<:promotion:1360153519415361546> Restaurant",
        value=f"**{store_info['name']}**",
        inline=False
    )

# Set store banner image if available
if store_info.get('banner_image'):
    embed.set_image(url=store_info['banner_image'])
```

## Available API Data Fields

### **Store Information** (from `data['store']`)
- ✅ `title` / `name` - Restaurant name
- ✅ `heroImageUrl` / `imageUrl` - Store banner/logo
- ✅ `rating` - Store rating (e.g., 4.5)
- ✅ `cuisineType` - Cuisine category
- ✅ `deliveryFee` - Base delivery fee
- ✅ `estimatedDeliveryTime` - Delivery time estimate
- 🔍 `description` - Store description
- 🔍 `address` - Store address
- 🔍 `phoneNumber` - Store contact
- 🔍 `hours` - Operating hours

### **Order Information** (from main data object)
- ✅ `storeUuid` - Store identifier
- ✅ `shoppingCart.items` - Cart items
- ✅ `deliveryAddress` - Delivery location
- 🔍 `orderInstructions` - Special delivery notes
- 🔍 `scheduledDeliveryTime` - Scheduled delivery
- 🔍 `groupOrderSettings` - Group order configuration

### **Item Information** (from cart items)
- ✅ `title` - Item name
- ✅ `price` - Item price
- ✅ `quantity` - Item quantity
- ✅ `imageURL` - Item image
- 🔍 `customizations` - Item modifications
- 🔍 `specialInstructions` - Item notes
- 🔍 `nutritionalInfo` - Nutritional data

## Potential Future Enhancements

### **High Priority** 🟢
1. **Store Rating Display**: Show store rating with stars in embed
2. **Cuisine Type**: Display cuisine category (e.g., "American", "Italian")
3. **Delivery Time**: Show estimated delivery time
4. **Store Hours**: Display current operating status

### **Medium Priority** 🟡
1. **Item Images**: Show thumbnails for cart items
2. **Store Description**: Brief store description
3. **Nutritional Information**: Calorie counts for health-conscious users
4. **Special Instructions**: Display order notes and customizations

### **Low Priority** 🔴
1. **Store Contact Info**: Phone number and address
2. **Scheduled Delivery**: Show if order is scheduled for later
3. **Group Order Settings**: Display group order configuration
4. **Promotional Banners**: Show active store promotions

## Implementation Recommendations

### **Immediate (Next Release)**
```python
# Add to embed template
if store_info.get('rating'):
    rating_stars = "⭐" * int(float(store_info['rating']))
    embed.add_field(
        name="⭐ Rating",
        value=f"{rating_stars} ({store_info['rating']})",
        inline=True
    )

if store_info.get('cuisine_type'):
    embed.add_field(
        name="🍽️ Cuisine",
        value=store_info['cuisine_type'],
        inline=True
    )
```

### **Future Considerations**
- **Performance**: Monitor API response times with additional data extraction
- **Rate Limiting**: Ensure additional API calls don't exceed limits
- **Error Handling**: Graceful fallbacks when store data is unavailable
- **Caching**: Consider caching store information for frequently used restaurants

## Testing Results

**Test URL**: `https://eats.uber.com/group-orders/11fc4eba-6863-4446-9900-aa50e502f293/join?source=quickActionCopy`

**Expected Enhancements**:
- ✅ Restaurant name displayed in embed
- ✅ Store banner image shown (if available)
- ✅ Additional metadata extracted for future use
- ✅ Backward compatibility maintained

## Conclusion

The implemented enhancements successfully extract and display store information in order summary embeds, providing users with more context about their orders. The modular approach allows for easy addition of future enhancements based on available API data.

**Files Modified**:
- `common/check_group_order.py` - Enhanced data extraction
- `themethodbot/embed_templates.py` - Updated embed templates

**Impact**: Improved user experience with richer order information display.
