import discord
import logging
import time
from typing import Dict, Any

# Configure logging
logger = logging.getLogger('themethodbot.paymentapp')

# Import payment methods from common
from themethodbot.common.bot import cerv, nelo, Glitchyz

class PaymentMethodButtons(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Cerv", style=discord.ButtonStyle.primary)
    async def cerv_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        logger.info(f"🔘 'Cerv Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await cerv(interaction)

    @discord.ui.button(label="Nelo", style=discord.ButtonStyle.success)
    async def nelo_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        logger.info(f"🔘 'Nelo's Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await nelo(interaction)

    @discord.ui.button(label="Glitch", style=discord.ButtonStyle.danger)
    async def glitchyz_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        logger.info(f"🔘 'Glitch's Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await Glitchyz(interaction)

class PaymentMethodSelector(discord.ui.View):
    def __init__(self, command_metrics: Dict[str, Dict[str, float]] = None):
        super().__init__(timeout=None)
        self.command_metrics = command_metrics or {}

    @discord.ui.button(label="💳 Payment Methods", style=discord.ButtonStyle.blurple)
    async def payment_methods(self, interaction: discord.Interaction, _: discord.ui.Button):
        # Track button click
        start_time = time.time()

        try:
            embed = discord.Embed(
                title="<:credit:1360153483747131442> Payment Methods",
                description="**Select the person handling your order:**",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Add a thumbnail for a modern look
            embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360134428197785702/tap-to-pay.gif?ex=67fa0365&is=67f8b1e5&hm=13930e1cd191c164666c4aa0f516c52f6bddfa7eb1cfa6d3fac6dd7f997624fd&=")  # Payment icon

            # Add spacing to the description
            embed.description += "\n\n"

            embed.add_field(
                name="<:information:1360153487295381576> Instructions",
                value="Click one of the buttons below to see the payment options for that person.",
                inline=False
            )

            # Set footer with helpful tip
            embed.set_footer(text="Tip: Make sure to use Friends & Family for PayPal/Venmo payments")

            # Send as ephemeral message (only visible to the user who clicked)
            await interaction.response.send_message(embed=embed, view=PaymentMethodButtons(), ephemeral=True)

            # Track metrics
            execution_time = time.time() - start_time
            if self.command_metrics is not None:
                if "payment_methods_button" not in self.command_metrics:
                    self.command_metrics["payment_methods_button"] = {'count': 0, 'total_time': 0, 'max_time': 0}

                self.command_metrics["payment_methods_button"]['count'] += 1
                self.command_metrics["payment_methods_button"]['total_time'] += execution_time
                self.command_metrics["payment_methods_button"]['max_time'] = max(
                    self.command_metrics["payment_methods_button"]['max_time'],
                    execution_time
                )

            logger.debug(f"Button payment_methods executed in {execution_time:.4f}s")
        except Exception as e:
            logger.error(f"Error in payment_methods button: {e}")
            logger.error(f"Traceback: {discord.utils.get_traceback(e)}")
            if not interaction.response.is_done():
                await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

def setup_payment_views(bot, command_metrics: Dict[str, Dict[str, float]] = None):
    """Register persistent views for payment functionality."""
    try:
        # Create and add the payment method selector view
        payment_selector = PaymentMethodSelector(command_metrics)
        bot.add_view(payment_selector)

        # Create and add the payment method buttons view
        payment_buttons = PaymentMethodButtons()
        bot.add_view(payment_buttons)

        logger.info("✅ Payment views registered successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Error setting up payment views: {e}")
        return False
