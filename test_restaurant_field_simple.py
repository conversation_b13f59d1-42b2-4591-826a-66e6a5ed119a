#!/usr/bin/env python3
"""
Simple test to verify the enhanced restaurant field is working correctly.
"""

import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

# Mock discord module since we're just testing the embed creation logic
class MockEmbed:
    def __init__(self, title="", description="", color=None):
        self.title = title
        self.description = description
        self.color = color
        self.fields = []
        self.image = None
        self.thumbnail = None
        self.footer = None
        
    def add_field(self, name, value, inline=False):
        self.fields.append({
            'name': name,
            'value': value,
            'inline': inline
        })
        
    def set_image(self, url):
        self.image = url
        
    def set_thumbnail(self, url):
        self.thumbnail = url
        
    def set_footer(self, text):
        self.footer = text

class MockColor:
    @staticmethod
    def from_rgb(r, g, b):
        return f"rgb({r},{g},{b})"

# Mock discord module
sys.modules['discord'] = type('MockDiscord', (), {
    'Embed': MockEmbed,
    'Color': MockColor
})()

# Mock logging
import logging
logging.basicConfig(level=logging.INFO)

# Now import our functions
try:
    from themethodbot.embed_templates import create_order_summary_embed, format_store_hours
    print("✅ Successfully imported enhanced embed functions")
except ImportError as e:
    print(f"❌ Failed to import: {e}")
    sys.exit(1)

def test_restaurant_field():
    """Test the enhanced restaurant field functionality."""
    print("\n=== Testing Enhanced Restaurant Field ===")
    
    # Test data with store information
    test_result = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {
            'name': "McDonald's",
            'delivery_time': '25-35',
            'is_open': True,
            'hours': 'Open until 11:00 PM'
        },
        'store_url': 'https://www.ubereats.com/store/mcdonalds-test',
        'location': {
            'address': '123 Main St',
            'city': 'New York',
            'state': 'NY',
            'zipcode': '10001'
        }
    }
    
    test_cart_items = [
        'Big Mac x1 ($5.99)',
        'Large Fries x1 ($2.99)',
        'Coca-Cola x1 ($1.99)'
    ]
    
    test_fee_calculations = {
        'subtotal': 10.97,
        'is_cad': False
    }
    
    try:
        # Create the embed
        embed = create_order_summary_embed(test_result, test_cart_items, test_fee_calculations)
        
        print("✅ Embed created successfully!")
        print(f"Title: {embed.title}")
        print(f"Description: {embed.description}")
        print(f"Number of fields: {len(embed.fields)}")
        
        # Look for the restaurant field
        restaurant_field = None
        for field in embed.fields:
            print(f"\nField: {field['name']}")
            print(f"Value: {field['value']}")
            if 'Restaurant' in field['name']:
                restaurant_field = field
                
        if restaurant_field:
            print(f"\n✅ Found restaurant field!")
            print(f"Restaurant field content:\n{restaurant_field['value']}")
            
            # Check if it contains expected elements
            expected_elements = ["McDonald's", "25-35", "Currently Open", "View on Uber Eats"]
            found_elements = []
            
            for element in expected_elements:
                if element in restaurant_field['value']:
                    found_elements.append(element)
                    
            print(f"\n✅ Found {len(found_elements)}/{len(expected_elements)} expected elements:")
            for element in found_elements:
                print(f"  - {element}")
                
            if len(found_elements) == len(expected_elements):
                print("\n🎉 All expected elements found! Restaurant field is working correctly.")
            else:
                missing = set(expected_elements) - set(found_elements)
                print(f"\n⚠️ Missing elements: {missing}")
        else:
            print("\n❌ Restaurant field not found in embed!")
            
    except Exception as e:
        print(f"❌ Error creating embed: {e}")
        import traceback
        traceback.print_exc()

def test_format_store_hours():
    """Test the format_store_hours function."""
    print("\n=== Testing format_store_hours Function ===")
    
    test_cases = [
        {
            'name': 'Open with hours',
            'store_info': {'is_open': True, 'hours': 'Open until 11:00 PM'},
            'expected': 'Currently Open | Open until 11:00 PM'
        },
        {
            'name': 'Closed',
            'store_info': {'is_open': False},
            'expected': 'Currently Closed'
        },
        {
            'name': 'Status string',
            'store_info': {'status': 'open', 'hours': 'Open 24 hours'},
            'expected': 'Currently Open | Open 24 hours'
        }
    ]
    
    for test_case in test_cases:
        try:
            result = format_store_hours(test_case['store_info'])
            print(f"✅ {test_case['name']}: '{result}'")
            
            if test_case['expected'] in result or result in test_case['expected']:
                print(f"  ✅ Contains expected content")
            else:
                print(f"  ⚠️ Expected: '{test_case['expected']}', Got: '{result}'")
                
        except Exception as e:
            print(f"❌ {test_case['name']}: Error - {e}")

def test_edge_cases():
    """Test edge cases."""
    print("\n=== Testing Edge Cases ===")
    
    # Test with no store info
    test_result_no_store = {
        'group_link': 'https://eats.uber.com/group-orders/test-uuid/join',
        'store_info': {},
        'location': {'address': '123 Main St'}
    }
    
    try:
        embed = create_order_summary_embed(test_result_no_store, [], {'subtotal': 0, 'is_cad': False})
        
        # Check if restaurant field exists
        restaurant_field = None
        for field in embed.fields:
            if 'Restaurant' in field['name']:
                restaurant_field = field
                break
                
        if restaurant_field:
            print("⚠️ Restaurant field found when no store info provided")
        else:
            print("✅ No restaurant field when no store info (expected)")
            
    except Exception as e:
        print(f"❌ Error with no store info: {e}")

if __name__ == "__main__":
    print("Testing Enhanced Restaurant Field Implementation")
    print("=" * 50)
    
    test_format_store_hours()
    test_restaurant_field()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("Testing complete!")
